'use client'

import { useState, useCallback } from 'react'
import Header from "@/components/header"
import Footer from "@/components/footer"
import Image from "next/image"

// ✅ Optimización: Array estático fuera del componente
const VIDEOS = [
  {
    id: 1,
    title: "LOUIS VUITTON x Yayoi Kusama YK Neverfull MM",
    thumbnail: "/images/catalog/videos/LOUIS-VUITTON-x-Yayoi-kusama-YK-Neverfull-MM-UNBOXINGLV-NEW-RELEASE-2023_HIGH.mp4",
    duration: "4:15",
    videoUrl: "/images/catalog/videos/LOUIS-VUITTON-x-Yayoi-kusama-YK-Neverfull-MM-UNBOXINGLV-NEW-RELEASE-2023_HIGH.mp4",
    isAvailable: true,
    brand: "<PERSON> Vuitton"
  },
  {
    id: 2,
    title: "BALENCIAGA Neo Cagole City Unboxing",
    thumbnail: "/images/catalog/videos/BALENCIAGA-Neo-Cagole-City-Unboxing_HIGH.mp4",
    duration: "3:17",
    videoUrl: "/images/catalog/videos/BALENCIAGA-Neo-Cagole-City-Unboxing_HIGH.mp4",
    isAvailable: true,
    brand: "Balenciaga"
  },
  {
    id: 3,
    title: "Bottega Veneta Mini Pouch - Before You Buy",
    thumbnail: "/images/catalog/videos/Bottega-Veneta-Mini-Pouch-_BEFORE-YOU-BUY__HIGH.mp4",
    duration: "4:32",
    videoUrl: "/images/catalog/videos/Bottega-Veneta-Mini-Pouch-_BEFORE-YOU-BUY__HIGH.mp4",
    isAvailable: true,
    brand: "Bottega Veneta"
  },
  {
    id: 4,
    title: "CHANEL Crossbody Shoulder Bag Review",
    thumbnail: "/images/catalog/videos/CHANEL-Crossbody-Shoulder-Bag-Red-wine-caviar-leather-detail-review-one-of-.mp4",
    duration: "5:45",
    videoUrl: "/images/catalog/videos/CHANEL-Crossbody-Shoulder-Bag-Red-wine-caviar-leather-detail-review-one-of-.mp4",
    isAvailable: true,
    brand: "Chanel"
  },
  {
    id: 5,
    title: "CHANEL Hobo Bag Unboxing and Detail Review",
    thumbnail: "/images/catalog/videos/CHANLE-HOBO-BAG-UNBOXING-AND-DETAIL-REVIEW_HIGH.mp4",
    duration: "6:12",
    videoUrl: "/images/catalog/videos/CHANLE-HOBO-BAG-UNBOXING-AND-DETAIL-REVIEW_HIGH.mp4",
    isAvailable: true,
    brand: "Chanel"
  },
  {
    id: 6,
    title: "DIOR Saddle Bag Grained Leather Review",
    thumbnail: "/images/catalog/videos/DIOR-SADDLE-BAG-GRAINED-LEATHER-UNBOXING-AND-DETAIL-REVIEW_HIGH.mp4",
    duration: "4:28",
    videoUrl: "/images/catalog/videos/DIOR-SADDLE-BAG-GRAINED-LEATHER-UNBOXING-AND-DETAIL-REVIEW_HIGH.mp4",
    isAvailable: true,
    brand: "Dior"
  },
  {
    id: 7,
    title: "Dior CD Signature Oval Camera Bag",
    thumbnail: "/images/catalog/videos/Dior-CD-SIGNATURE-OVAL-CAMERA-BAG-Black-VS-oblique-shoulder-bag-@unboxing_HIGH.mp4",
    duration: "3:55",
    videoUrl: "/images/catalog/videos/Dior-CD-SIGNATURE-OVAL-CAMERA-BAG-Black-VS-oblique-shoulder-bag-@unboxing_HIGH.mp4",
    isAvailable: true,
    brand: "Dior"
  },
  {
    id: 8,
    title: "GOYARD Saint-Pierre Card Wallet Unboxing",
    thumbnail: "/images/catalog/videos/GOYARD-BAG-Saint-Pierre-Card-Wallet-UNBOXING-2023.mp4",
    duration: "2:04",
    videoUrl: "/images/catalog/videos/GOYARD-BAG-Saint-Pierre-Card-Wallet-UNBOXING-2023.mp4",
    isAvailable: true,
    brand: "Goyard"
  },
  {
    id: 9,
    title: "GOYARD Belvedere PM II Black Review",
    thumbnail: "/images/catalog/videos/GOYARD-BELVEDERE-PM-II-BLACK-IN-DEPTH-REVIEW_HIGH.mp4",
    duration: "5:33",
    videoUrl: "/images/catalog/videos/GOYARD-BELVEDERE-PM-II-BLACK-IN-DEPTH-REVIEW_HIGH.mp4",
    isAvailable: true,
    brand: "Goyard"
  },
  {
    id: 10,
    title: "GUCCI GG Supreme Ophidia Medium Tote Review",
    thumbnail: "/images/catalog/videos/GUCCI-GG-Supreme-Ophidia-Medium-Tote-Review_HIGH.mp4",
    duration: "4:15",
    videoUrl: "/images/catalog/videos/GUCCI-GG-Supreme-Ophidia-Medium-Tote-Review_HIGH.mp4",
    isAvailable: true,
    brand: "Gucci"
  },
  {
    id: 11,
    title: "GUCCI Horsebit Chain Shoulder Bag Unboxing",
    thumbnail: "/images/catalog/videos/GUCCI-NEW-RELEASE-FW23-_HORSEBIT-CHAIN-SHOULDER-BAG-UNBOXING_HIGH.mp4",
    duration: "3:42",
    videoUrl: "/images/catalog/videos/GUCCI-NEW-RELEASE-FW23-_HORSEBIT-CHAIN-SHOULDER-BAG-UNBOXING_HIGH.mp4",
    isAvailable: true,
    brand: "Gucci"
  },
  {
    id: 12,
    title: "Goyard Rouette Bag Unboxing and Review",
    thumbnail: "/images/catalog/videos/Goyard-Rouette-Bag-Unboxing-and-Review_HIGH.mp4",
    duration: "4:08",
    videoUrl: "/images/catalog/videos/Goyard-Rouette-Bag-Unboxing-and-Review_HIGH.mp4",
    isAvailable: true,
    brand: "Goyard"
  },
  {
    id: 13,
    title: "HERMÈS Kelly Mini Unboxing and Detail Review",
    thumbnail: "/images/catalog/videos/HERMES-HANDBAG-COLLECTION-2023-_-Hermes-Kelly-Mini-Unboxing-and-Detail-Revie_HIGH.mp4",
    duration: "6:25",
    videoUrl: "/images/catalog/videos/HERMES-HANDBAG-COLLECTION-2023-_-Hermes-Kelly-Mini-Unboxing-and-Detail-Revie_HIGH.mp4",
    isAvailable: true,
    brand: "Hermès"
  },
  {
    id: 14,
    title: "Hermès Birkin Unboxing",
    thumbnail: "/images/catalog/videos/Hermes-Birkin-Unboxing_HIGH-1.mp4",
    duration: "3:33",
    videoUrl: "/images/catalog/videos/Hermes-Birkin-Unboxing_HIGH-1.mp4",
    isAvailable: true,
    brand: "Hermès"
  },
  {
    id: 15,
    title: "LOUIS VUITTON Diane Bag Unboxing",
    thumbnail: "/images/catalog/videos/LOUIS-VUITTON-BAG-UNBOXING-_LV-Diane-Bag-M45985-Try-on-_-WHAT-FIT-IN-MY-LV-B_HIGH.mp4",
    duration: "5:18",
    videoUrl: "/images/catalog/videos/LOUIS-VUITTON-BAG-UNBOXING-_LV-Diane-Bag-M45985-Try-on-_-WHAT-FIT-IN-MY-LV-B_HIGH.mp4",
    isAvailable: true,
    brand: "Louis Vuitton"
  },
  {
    id: 16,
    title: "LOUIS VUITTON New Wave Chain Bag Review",
    thumbnail: "/images/catalog/videos/LOUIS-VUITTON-Calfskin-LV-NEW-WAVE-CHAIN-BAG-UNBOXING-AND-REVIEW_HIGH.mp4",
    duration: "4:44",
    videoUrl: "/images/catalog/videos/LOUIS-VUITTON-Calfskin-LV-NEW-WAVE-CHAIN-BAG-UNBOXING-AND-REVIEW_HIGH.mp4",
    isAvailable: true,
    brand: "Louis Vuitton"
  },
  {
    id: 17,
    title: "LOUIS VUITTON Duo Slingbag Unboxing",
    thumbnail: "/images/catalog/videos/LOUIS-VUITTON-Duo-Slingbag-M21890-UNBOXING-2023_HIGH.mp4",
    duration: "3:22",
    videoUrl: "/images/catalog/videos/LOUIS-VUITTON-Duo-Slingbag-M21890-UNBOXING-2023_HIGH.mp4",
    isAvailable: true,
    brand: "Louis Vuitton"
  },
  {
    id: 18,
    title: "LOUIS VUITTON Zippy Wallet Unboxing",
    thumbnail: "/images/catalog/videos/LOUIS-VUITTON-NEW-RELEASE-2024_LV-ZIPPY-WALLET-UNBOXING-_SUMMER-COLLECTION-_HIGH.mp4",
    duration: "2:55",
    videoUrl: "/images/catalog/videos/LOUIS-VUITTON-NEW-RELEASE-2024_LV-ZIPPY-WALLET-UNBOXING-_SUMMER-COLLECTION-_HIGH.mp4",
    isAvailable: true,
    brand: "Louis Vuitton"
  }
]

// ✅ Optimización: Tipo para videos
type Video = typeof VIDEOS[number]

export default function VideosPage() {
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null)

  // ✅ Optimización: useCallback para handlers
  const handleVideoClick = useCallback((video: Video) => {
    if (video.isAvailable) {
      setSelectedVideo(video)
    }
  }, [])

  const closeModal = useCallback(() => {
    setSelectedVideo(null)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-white">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Video
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 ml-4">
                Collection
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Watch detailed unboxing videos and reviews of our high-end replica luxury fashion collection.
              See the quality and craftsmanship up close.
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto rounded-full"></div>
          </div>
        </div>
      </section>

      {/* Video Grid */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {VIDEOS.map((video) => (
              <div
                key={video.id}
                onClick={() => handleVideoClick(video)}
                className={`relative group overflow-hidden rounded-2xl shadow-lg ${
                  video.isAvailable ? 'cursor-pointer' : 'cursor-not-allowed'
                } hover:shadow-xl transition-all duration-300`}
              >
                {/* Video Thumbnail */}
                <div className="relative aspect-video">
                  <video
                    className="w-full h-full object-cover"
                    preload="metadata"
                    muted
                  >
                    <source src={video.thumbnail} type="video/mp4" />
                  </video>

                  {/* Title Overlay */}
                  <div className="absolute top-4 left-4 right-4">
                    <h3 className="text-white font-bold text-lg leading-tight drop-shadow-lg">
                      {video.title}
                    </h3>
                  </div>

                  {/* Duration Badge */}
                  <div className="absolute bottom-4 left-4">
                    <span className="bg-black/80 text-white px-2 py-1 rounded text-sm font-medium">
                      {video.duration}
                    </span>
                  </div>

                  {/* Play Button */}
                  <div className="absolute bottom-4 right-4">
                    {video.isAvailable ? (
                      <div className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center group-hover:bg-white transition-colors">
                        <svg className="w-5 h-5 text-gray-900 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M8 5v14l11-7z"/>
                        </svg>
                      </div>
                    ) : (
                      <div className="w-10 h-10 bg-orange-600/90 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* Video Controls Overlay */}
                  <div className="absolute inset-x-4 bottom-16 flex items-center gap-2">
                    {/* Mute Button */}
                    <button className="w-8 h-8 bg-black/60 rounded-full flex items-center justify-center text-white hover:bg-black/80 transition-colors">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                      </svg>
                    </button>

                    {/* Fullscreen Button */}
                    <button className="w-8 h-8 bg-black/60 rounded-full flex items-center justify-center text-white hover:bg-black/80 transition-colors">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                      </svg>
                    </button>

                    {/* More Options */}
                    <button className="w-8 h-8 bg-black/60 rounded-full flex items-center justify-center text-white hover:bg-black/80 transition-colors ml-auto">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                      </svg>
                    </button>
                  </div>

                  {/* Coming Soon Overlay */}
                  {!video.isAvailable && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <div className="text-center text-white">
                        <div className="text-lg font-bold mb-2">Coming Soon</div>
                        <div className="text-sm opacity-80">Video will be available shortly</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Video Modal */}
      {selectedVideo && (
        <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl overflow-hidden max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-900">{selectedVideo.title}</h2>
              <button
                onClick={closeModal}
                className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Video Player */}
            <div className="aspect-video bg-black">
              <video
                className="w-full h-full"
                controls
                autoPlay
                poster={selectedVideo.thumbnail}
                key={selectedVideo.id}
              >
                <source src={selectedVideo.videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>

            {/* Video Info */}
            <div className="p-6">
              <div className="flex items-center gap-4 mb-4">
                <span className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium">
                  {selectedVideo.duration}
                </span>
                <span className="text-gray-600">{selectedVideo.brand || 'Best Lyfe Fashion'}</span>
              </div>
              <p className="text-gray-700 leading-relaxed">
                Experience the elegance and luxury of high-end replica fashion. This exclusive showcase
                features premium pieces, timeless designs, and the sophisticated style that defines luxury fashion.
                See detailed unboxing and reviews of our carefully curated collection.
              </p>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  )
}

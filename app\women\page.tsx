"use client"

import Link from "next/link"
import Image from "next/image"
import { useState } from "react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import SzwegoModal from "@/components/szwego-modal"
import { useSzwegoModal } from "@/hooks/use-szwego-modal"

const categories = [
  {
    id: 1,
    name: "Women Boots",
    image: "/images/catalog/women/women-boots.png",
    description: "Stylish and comfortable boots for every occasion",
    gradient: "from-purple-400 to-pink-400",
    href: "https://wholesale22.x.yupoo.com/categories/4279940?page=2"
    
  },
  {
    id: 2,
    name: "Women Belts",
    image: "/images/catalog/women/women-belts.jpg",
    description: "Premium leather belts to complete your look",
    gradient: "from-amber-400 to-orange-400",
    href: "https://lh-bubaidu.x.yupoo.com"
  },
  {
    id: 3,
    name: "Sunglass<PERSON>",
    image: "/images/catalog/women/women-sunglasses.jpg",
    description: "Designer sunglasses for ultimate style",
    gradient: "from-gray-400 to-gray-600",
    href: "https://shangmei168.x.yupoo.com/albums"
  },
  {
    id: 4,
    name: "Women Bags",
    image: "/images/catalog/women/women-bags.jpeg",
    description: "Luxury handbags and accessories",
    gradient: "from-rose-400 to-red-400",
    href: "/women/bags"
  },
  {
    id: 5,
    name: "Wallets",
    image: "/images/catalog/women/wallets.jpg",
    description: "Stylish wallets and small accessories",
    gradient: "from-pink-400 to-rose-400",
    href: "https://a202211011505300820001582.wsxcme.com/static/index.html#/goods_list/_doQoQdE1IxUpwM4XUK8yBVkhlBszn89w-fu68Kw?tagId=54476533"
  },
  {
    id: 6,
    name: "Women Shoes",
    image: "/images/catalog/women/women-shoes.jpeg",
    description: "Elegant footwear for the modern woman",
    gradient: "from-blue-400 to-indigo-400",
    href: "/women/shoes"
  },
  {
    id: 7,
    name: "Hats",
    image: "/images/catalog/women/hats/men-hats.jpeg",
    description: "Fashionable hats for every season",
    gradient: "from-green-400 to-emerald-400",
    href: "https://wholesale22.x.yupoo.com/albums/202009218?uid=1&isSubCate=false&referrercate=4284211"
  },
  {
    id: 8,
    name: "Bikinis",
    image: "/images/catalog/women/women-bikini.jpeg",
    description: "Trendy swimwear for beach days",
    gradient: "from-cyan-400 to-blue-400",
    href: "https://wholesale22.x.yupoo.com/categories/4547587"
  },
  {
    id: 9,
    name: "Slippers/Slides",
    image: "/images/catalog/women/slippers.jpeg",
    description: "Comfortable slides for relaxation",
    gradient: "from-teal-400 to-cyan-400",
    href: "https://a202211011505300820001582.wsxcme.com/static/index.html#/goods_list/_doQoQdE1IxUpwM4XUK8yBVkhlBszn89w-fu68Kw?tagId=52413574"
  },
  {
    id: 10,
    name: "Jewelry",
    image: "/images/catalog/women/Women-Jewelry.jpeg",
    description: "Exquisite jewelry pieces",
    gradient: "from-yellow-400 to-amber-400",
    href: "https://wholesale22.x.yupoo.com/categories/3482474"
  },
  {
    id: 11,
    name: "Scarf",
    image: "/images/catalog/women/scarf.jpeg",
    description: "Elegant scarves for any outfit",
    gradient: "from-violet-400 to-purple-400",
    href: "https://a202211011505300820001582.wsxcme.com/static/index.html#/goods_list/_doQoQdE1IxUpwM4XUK8yBVkhlBszn89w-fu68Kw?tagId=52413574"
  },
  {
    id: 11,
    name: "Wallets",
    image: "/images/catalog/women/wallets.jpg",
    description: "Luxury wallets and small leather goods",
    gradient: "from-indigo-400 to-purple-400",
    href: "https://a202211011505300820001582.wsxcme.com/static/index.html#/goods_list/_doQoQdE1IxUpwM4XUK8yBVkhlBszn89w-fu68Kw?tagId=54476533"
  }
]

function CategoryCard({
  category,
  isSmall,
  onSzwegoClick
}: {
  category: typeof categories[0],
  isSmall: boolean,
  onSzwegoClick: (e: React.MouseEvent<HTMLAnchorElement>, url: string) => void
}) {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (category.href) {
      if (category.href.includes('szwego.com')) {
        // Create a synthetic anchor event for szwego links
        const syntheticEvent = {
          ...e,
          currentTarget: { href: category.href }
        } as React.MouseEvent<HTMLAnchorElement>
        onSzwegoClick(syntheticEvent, category.href)
      } else if (category.href.startsWith('http')) {
        // External link (non-szwego)
        window.open(category.href, '_blank', 'noopener,noreferrer')
      } else {
        // Internal link
        window.location.href = category.href
      }
    }
  }

  return (
    <div
      onClick={handleClick}
      className={`group relative bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 overflow-hidden cursor-pointer ${
        isSmall ? 'rounded-xl' : 'rounded-2xl'
      } ${category.href ? 'hover:ring-2 hover:ring-orange-400' : ''}`}
    >
      {/* Image Container */}
      <div className={`relative overflow-hidden ${
        isSmall ? 'h-40 rounded-t-xl' : 'h-64 rounded-t-2xl'
      }`}>
        {/* Loading skeleton */}
        {isLoading && (
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse"></div>
        )}

        {/* Fallback gradient background */}
        <div className={`absolute inset-0 bg-gradient-to-br ${category.gradient} ${imageError ? 'opacity-90' : 'opacity-30'}`}></div>

        {/* Main Image */}
        {!imageError && (
          <Image
            src={category.image}
            alt={category.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            onError={() => setImageError(true)}
            onLoad={() => setIsLoading(false)}
            priority={category.id <= 4}
          />
        )}

        {/* Category Icon for fallback */}
        {imageError && (
          <div className="absolute inset-0 flex items-center justify-center z-20">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
              <span className="text-2xl text-white font-bold">
                {category.name.charAt(0)}
              </span>
            </div>
          </div>
        )}



        {/* Enhanced overlay on hover */}
        <div className="absolute inset-0 bg-gradient-to-t from-orange-600/90 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 z-30 flex flex-col items-center justify-center gap-4">
          {!isSmall && (
            <div className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500 delay-200">
              <p className="text-white text-center text-sm px-4 opacity-90">
                {category.description}
              </p>
              {category.href && (
                <div className="mt-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
                  <span className="text-white text-xs font-medium">Click to View</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className={`${isSmall ? 'p-3' : 'p-6'}`}>
        <h3 className={`font-bold text-gray-900 group-hover:text-orange-600 transition-colors duration-200 ${
          isSmall ? 'text-sm mb-1 text-center' : 'text-xl mb-2'
        }`}>
          {category.name}
        </h3>
        {!isSmall && (
          <p className="text-gray-600 text-sm leading-relaxed">
            {category.description}
          </p>
        )}
      </div>

      {/* Decorative element */}
      <div className="absolute top-4 right-4 w-8 h-8 bg-orange-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
        {category.href ? (
          category.href.startsWith('http') ? (
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
          ) : (
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          )
        ) : (
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        )}
      </div>
    </div>
  )
}

export default function WomenPage() {
  const [isSmallView, setIsSmallView] = useState(false)
  const { isModalOpen, redirectUrl, handleSzwegoClick, closeModal } = useSzwegoModal()

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-8 sm:py-20 px-4">
        <div className="container mx-auto">
          <div className="text-center">
            <div className="max-w-4xl mx-auto relative">
              {/* Filter Button - Top Right */}
              <div className="absolute top-0 right-0 sm:relative sm:top-auto sm:right-auto sm:flex sm:justify-end sm:mb-4">
                <button
                  onClick={() => setIsSmallView(!isSmallView)}
                  className="bg-orange-600 text-white p-2 sm:p-3 rounded-lg hover:bg-orange-700 transition-colors duration-200 shadow-lg flex items-center gap-2 text-xs sm:text-sm"
                  title={isSmallView ? "Switch to Large View" : "Switch to Small View"}
                >
                  {isSmallView ? (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                    </svg>
                  )}
                </button>
              </div>

              <h1 className="text-3xl sm:text-5xl md:text-6xl font-bold text-gray-900 mb-4 sm:mb-6">
                Women's
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 ml-2 sm:ml-4">
                  Collection
                </span>
              </h1>
              <h2 className="text-2xl sm:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">Shop by Category</h2>
              <p className="text-base sm:text-xl text-gray-600 mb-6 sm:mb-8 leading-relaxed px-4 sm:px-0">
                Explore our diverse range of women's fashion categories, each carefully selected
                to bring you the latest trends and timeless classics.
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto rounded-full"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Grid */}
      <section className="pt-4 pb-16 px-4">
        <div className="container mx-auto">

          <div className={`grid ${
            isSmallView
              ? 'grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4'
              : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8'
          }`}>
            {categories.map((category) => (
              <CategoryCard
                key={category.id}
                category={category}
                isSmall={isSmallView}
                onSzwegoClick={handleSzwegoClick}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Elevate Your Style?
            </h2>
            <p className="text-xl text-orange-100 mb-8 leading-relaxed">
              Join thousands of fashion-forward women who trust Best Lyfe Fashion
              for their style needs. Get exclusive access to new arrivals and special offers.
            </p>
          </div>
        </div>
      </section>

      <Footer />

      {/* Szwego Modal */}
      <SzwegoModal
        isOpen={isModalOpen}
        onClose={closeModal}
        redirectUrl={redirectUrl}
      />
    </div>
  )
}

const fs = require('fs');
const path = require('path');

console.log('📦 Creating deployment package for BanaHosting...');

// Files and folders to include in deployment
const deploymentFiles = [
  '.next',
  'public',
  'package.json',
  'package-lock.json',
  'next.config.js',
  '.env.production',
  'ecosystem.config.js',
  'DEPLOYMENT.md'
];

// Create deployment directory
const deployDir = './deployment-package';
if (!fs.existsSync(deployDir)) {
  fs.mkdirSync(deployDir);
}

console.log('✅ Deployment package structure ready!');
console.log('');
console.log('📋 Files to upload to BanaHosting:');
deploymentFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} (missing)`);
  }
});

console.log('');
console.log('🚀 Next steps:');
console.log('1. Compress these files into a ZIP');
console.log('2. Upload to your BanaHosting cPanel File Manager');
console.log('3. Extract in public_html directory');
console.log('4. Follow DEPLOYMENT.md instructions');
console.log('');
console.log('🌐 Your site will be live at: https://bestlyfefashion.com');

import { Button } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"

// ✅ Optimización: Array estático fuera del componente
const FEATURES = [
  "Premium Designer Brands",
  "Authentic Luxury Items",
  "Exclusive Collections"
] as const

export default function WomanFashionSection() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-12 items-center max-w-7xl mx-auto">

          {/* Text Content - Left Side */}
          <div className="space-y-8">
            <div className="space-y-6">
              <div className="inline-block">
                <span className="bg-gradient-to-r from-orange-400 to-orange-600 text-white px-4 py-2 rounded-full text-sm font-bold">
                  ✨ Featured Collection
                </span>
              </div>

              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                Elegant
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 block">
                  Women's Fashion
                </span>
              </h2>

              <p className="text-lg md:text-xl text-gray-600 leading-relaxed">
                Discover our curated collection of luxury women's fashion. From timeless classics to contemporary trends,
                each piece is carefully selected to embody sophistication and style.
              </p>

              <div className="space-y-4">
                {FEATURES.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-center">
              <Link href="/women">
                <Button className="bg-gradient-to-r from-orange-400 to-orange-600 hover:from-orange-500 hover:to-orange-700 text-white font-bold px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  Shop Women's Collection
                  <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Button>
              </Link>
            </div>
          </div>

          {/* Image Content - Right Side */}
          <div className="relative">
            <div className="w-full h-[700px] relative rounded-2xl overflow-hidden">
              <Image
                src="/images/womanfashion.png"
                alt="Elegant women's fashion collection"
                fill
                className="object-contain"
                priority
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

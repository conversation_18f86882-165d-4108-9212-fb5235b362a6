'use client'

import { useState, useMemo, useCallback } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import Header from "@/components/header"
import Footer from "@/components/footer"
import SzwegoModal from "@/components/szwego-modal"
import { useSzwegoModal } from "@/hooks/use-szwego-modal"

// ✅ Optimización: Arrays estáticos fuera del componente
const WOMEN_CATEGORIES = [
  {
    id: 1,
    name: "Women Boots",
    image: "/images/catalog/women/women-boots.png",
    description: "Stylish and comfortable boots for every occasion",
    gradient: "from-purple-400 to-pink-400",
    href: "https://wholesale22.x.yupoo.com/categories/4279940?page=2"
  },
  {
    id: 2,
    name: "Women Belts",
    image: "/images/catalog/women/women-belts.jpg",
    description: "Premium leather belts to complete your look",
    gradient: "from-amber-400 to-orange-400",
    href: "https://lh-bubaidu.x.yupoo.com"
  },
  {
    id: 3,
    name: "Sunglasses",
    image: "/images/catalog/women/women-sunglasses.jpg",
    description: "Designer sunglasses for ultimate style",
    gradient: "from-gray-400 to-gray-600",
    href: "https://shangmei168.x.yupoo.com/albums"
  },
  {
    id: 4,
    name: "Women Bags",
    image: "/images/catalog/women/women-bags.jpeg",
    description: "Luxury handbags and accessories",
    gradient: "from-rose-400 to-red-400",
    href: "/women/bags"
  },
  {
    id: 5,
    name: "Women Shoes",
    image: "/images/catalog/women/women-shoes.jpeg",
    description: "Elegant footwear for the modern woman",
    gradient: "from-blue-400 to-indigo-400",
    href: "/women/shoes"
  },
  {
    id: 6,
    name: "Hats",
    image: "/images/catalog/women/hats/men-hats.jpeg",
    description: "Fashionable hats for every season",
    gradient: "from-green-400 to-emerald-400",
    href: "https://wholesale22.x.yupoo.com/albums/202009218?uid=1&isSubCate=false&referrercate=4284211"
  },
  {
    id: 7,
    name: "Bikinis",
    image: "/images/catalog/women/women-bikini.jpeg",
    description: "Trendy swimwear for beach days",
    gradient: "from-cyan-400 to-blue-400",
    href: "https://wholesale22.x.yupoo.com/categories/4547587"
  },
  {
    id: 8,
    name: "Slippers/Slides",
    image: "/images/catalog/women/slippers.jpeg",
    description: "Comfortable slides for relaxation",
    gradient: "from-teal-400 to-cyan-400",
    href: "https://a202211011505300820001582.wsxcme.com/static/index.html#/goods_list/_doQoQdE1IxUpwM4XUK8yBVkhlBszn89w-fu68Kw?tagId=52413574"
  },
  {
    id: 9,
    name: "Jewelry",
    image: "/images/catalog/women/Women-Jewelry.jpeg",
    description: "Exquisite jewelry pieces",
    gradient: "from-yellow-400 to-amber-400",
    href: "https://a202211011505300820001582.wsxcme.com/static/index.html#/goods_list/_doQoQdE1IxUpwM4XUK8yBVkhlBszn89w-fu68Kw?tagId=52413574"
  },
  {
    id: 10,
    name: "Scarf",
    image: "/images/catalog/women/scarf.jpeg",
    description: "Elegant scarves for any outfit",
    gradient: "from-violet-400 to-purple-400",
    href: "https://a202211011505300820001582.wsxcme.com/static/index.html#/goods_list/_doQoQdE1IxUpwM4XUK8yBVkhlBszn89w-fu68Kw?tagId=52413574"
  },
  {
    id: 11,
    name: "Wallets",
    image: "/images/catalog/women/wallets.jpg",
    description: "Luxury wallets and small leather goods",
    gradient: "from-indigo-400 to-purple-400",
    href: "https://a202211011505300820001582.wsxcme.com/static/index.html#/goods_list/_doQoQdE1IxUpwM4XUK8yBVkhlBszn89w-fu68Kw?tagId=54476533"
  }
] as const

const MEN_CATEGORIES = [
  {
    id: 11,
    name: "Men Bags",
    image: "/images/catalog/men/bags/men-bags.jpeg",
    description: "Premium leather bags and briefcases for the modern man",
    gradient: "from-gray-600 to-gray-800",
    href: "/men/bags"
  },
  {
    id: 12,
    name: "Men Belts",
    image: "/images/catalog/men/belts/men belts.jpeg",
    description: "Elegant leather belts for every occasion",
    gradient: "from-amber-600 to-orange-600",
    href: "https://lh-bubaidu.x.yupoo.com"
  },
  {
    id: 13,
    name: "Sunglasses",
    image: "/images/catalog/men/sunglasses/men-sunglasses.jpeg",
    description: "Designer sunglasses for ultimate style",
    gradient: "from-slate-500 to-gray-700",
    href: "https://shangmei168.x.yupoo.com/albums"
  },
  {
    id: 14,
    name: "Travel",
    image: "/images/catalog/men/travel/men-travel.jpeg",
    description: "Travel essentials and luggage for adventures",
    gradient: "from-blue-600 to-indigo-600",
    href: "https://wholesale22.x.yupoo.com/albums/*********?uid=1&isSubCate=false&referrercate=4460021"
  },
  {
    id: 15,
    name: "Men Shoes",
    image: "/images/catalog/men/shoes/men-shoes.jpeg",
    description: "Premium footwear for the sophisticated gentleman",
    gradient: "from-stone-600 to-neutral-700",
    href: "/men/shoes"
  },
  {
    id: 16,
    name: "Children Shoes",
    image: "/images/catalog/men/children/children-shoes.jpeg",
    description: "Comfortable and stylish shoes for kids",
    gradient: "from-emerald-500 to-teal-600",
    href: "https://wholesale22.x.yupoo.com/albums/*********?uid=1&isSubCate=false&referrercate=4460021"
  }
] as const

// ✅ Optimización: Tipo para las categorías
type Category = (typeof WOMEN_CATEGORIES)[number] | (typeof MEN_CATEGORIES)[number]

// ✅ Optimización: Memorizar el componente CategoryCard
const CategoryCard = ({
  category,
  isSmall,
  onSzwegoClick
}: {
  category: Category,
  isSmall: boolean,
  onSzwegoClick?: (e: React.MouseEvent<HTMLAnchorElement>, url: string) => void
}) => {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // ✅ Optimización: useCallback para handlers
  const handleImageError = useCallback(() => setImageError(true), [])
  const handleImageLoad = useCallback(() => setIsLoading(false), [])

  return (
    <div className={`group relative bg-white shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 overflow-hidden cursor-pointer ${
      isSmall ? 'rounded-xl' : 'rounded-2xl'
    }`}>
      {/* Image Container */}
      <div className={`relative overflow-hidden ${
        isSmall ? 'h-40 rounded-t-xl' : 'h-64 rounded-t-2xl'
      }`}>
        {/* Loading skeleton */}
        {isLoading && (
          <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse"></div>
        )}
        
        {/* Fallback gradient background */}
        <div className={`absolute inset-0 bg-gradient-to-br ${category.gradient} ${imageError ? 'opacity-90' : 'opacity-30'}`}></div>

        {/* Main Image */}
        {!imageError && (
          <Image
            src={category.image}
            alt={category.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-700 ease-out"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            onError={handleImageError}
            onLoad={handleImageLoad}
            priority={category.id <= 4}
          />
        )}
        
        {/* Category Icon for fallback */}
        {imageError && (
          <div className="absolute inset-0 flex items-center justify-center z-20">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center animate-pulse">
              <span className="text-2xl text-white font-bold">
                {category.name.charAt(0)}
              </span>
            </div>
          </div>
        )}
        
        {/* Enhanced overlay on hover */}
        <div className="absolute inset-0 bg-gradient-to-t from-orange-600/90 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 z-30 flex flex-col items-center justify-center gap-4">
          <div className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500 delay-100">
            {category.href ? (
              category.href.startsWith('http') ? (
                category.href.includes('szwego.com') && onSzwegoClick ? (
                  <a
                    href={category.href}
                    onClick={(e) => onSzwegoClick(e, category.href!)}
                    className="bg-white text-orange-600 px-8 py-3 rounded-full font-bold hover:bg-orange-50 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 inline-block"
                  >
                    Click to View
                  </a>
                ) : (
                  <a
                    href={category.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white text-orange-600 px-8 py-3 rounded-full font-bold hover:bg-orange-50 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 inline-block"
                  >
                    Click to View
                  </a>
                )
              ) : (
                <Link
                  href={category.href}
                  className="bg-white text-orange-600 px-8 py-3 rounded-full font-bold hover:bg-orange-50 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 inline-block"
                >
                  Click to View
                </Link>
              )
            ) : (
              <button className="bg-white text-orange-600 px-8 py-3 rounded-full font-bold hover:bg-orange-50 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105">
                Click to View
              </button>
            )}
          </div>
          {!isSmall && (
            <div className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500 delay-200">
              <p className="text-white text-center text-sm px-4 opacity-90">
                {category.description}
              </p>
            </div>
          )}
        </div>
        
        {/* Favorite button */}
        <div className="absolute top-3 right-3 z-40">
          <button className="w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-white hover:scale-110">
            <svg className="w-4 h-4 text-gray-600 hover:text-red-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className={`p-4 ${isSmall ? 'p-3' : 'p-6'}`}>
        <h3 className={`font-bold text-gray-900 mb-2 ${isSmall ? 'text-sm' : 'text-xl'}`}>
          {category.name}
        </h3>
        {!isSmall && (
          <p className="text-gray-600 text-sm leading-relaxed">
            {category.description}
          </p>
        )}
      </div>
    </div>
  )
}

export default function CollectionPage() {
  const [isSmallView, setIsSmallView] = useState(false)
  const { isModalOpen, redirectUrl, handleSzwegoClick, closeModal } = useSzwegoModal()

  // ✅ Optimización: useCallback para toggle de vista
  const toggleView = useCallback(() => {
    setIsSmallView(prev => !prev)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-white">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto relative">
            {/* Filter Button - Top Right */}
            <div className="absolute top-0 right-0 sm:relative sm:top-auto sm:right-auto sm:flex sm:justify-end sm:mb-4">
              <button
                onClick={toggleView}
                className="bg-orange-600 text-white p-2 sm:p-3 rounded-lg hover:bg-orange-700 transition-colors duration-200 shadow-lg flex items-center gap-2 text-xs sm:text-sm"
                title={isSmallView ? "Switch to Large View" : "Switch to Small View"}
              >
                {isSmallView ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                )}
              </button>
            </div>
            
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Complete
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 ml-4">
                Collection
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Discover our full range of premium fashion for both men and women, 
              carefully curated to bring you the finest in contemporary style.
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto rounded-full"></div>
          </div>
        </div>
      </section>

      {/* Women's Section */}
      <section className="pt-4 pb-16 px-4">
        <div className="container mx-auto">
          <div className="mb-8">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Women's Collection</h2>
            <p className="text-lg text-gray-600">
              Elegant and sophisticated pieces for the modern woman
            </p>
          </div>

          <div className={`grid ${
            isSmallView 
              ? 'grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4' 
              : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8'
          } mb-16`}>
            {WOMEN_CATEGORIES.map((category) => (
              <CategoryCard
                key={category.id}
                category={category}
                isSmall={isSmallView}
                onSzwegoClick={handleSzwegoClick}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Men's Section */}
      <section className="pt-4 pb-16 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="mb-8">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Men's Collection</h2>
            <p className="text-lg text-gray-600">
              Premium essentials and sophisticated style for the modern gentleman
            </p>
          </div>

          <div className={`grid ${
            isSmallView
              ? 'grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4'
              : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8'
          }`}>
            {MEN_CATEGORIES.map((category) => (
              <CategoryCard
                key={category.id}
                category={category}
                isSmall={isSmallView}
                onSzwegoClick={handleSzwegoClick}
              />
            ))}
          </div>
        </div>
      </section>

      <Footer />

      {/* Szwego Modal */}
      <SzwegoModal
        isOpen={isModalOpen}
        onClose={closeModal}
        redirectUrl={redirectUrl}
      />
    </div>
  )
}

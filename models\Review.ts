import mongoose, { Document, Schema } from 'mongoose'

export interface IReview extends Document {
  name: string
  title: string
  rating: number
  date: string
  review: string
  fullReview: string
  email: string
  isApproved: boolean
  createdAt: Date
  updatedAt: Date
  
}

export interface CreateReviewData {
  name: string
  title: string
  rating: number
  review: string
  email: string
}

const ReviewSchema = new Schema<IReview>({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot exceed 5']
  },
  date: {
    type: String,
    required: true
  },
  review: {
    type: String,
    required: [true, 'Review content is required'],
    trim: true,
    maxlength: [2000, 'Review cannot exceed 2000 characters']
  },
  fullReview: {
    type: String,
    required: true,
    trim: true
  },


  email: {
    type: String,
    required: [true, 'Email is required'],
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  isApproved: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true // This automatically adds createdAt and updatedAt
})

// Create indexes for better performance
ReviewSchema.index({ isApproved: 1, createdAt: -1 })
ReviewSchema.index({ rating: 1 })

// Export the model
const Review = mongoose.models.Review || mongoose.model<IReview>('Review', ReviewSchema)

export default Review

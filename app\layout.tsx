import type { Metadata } from 'next'
import { Inter, Playfair_Display } from 'next/font/google'
import './globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

const playfair = Playfair_Display({ 
  subsets: ['latin'],
  variable: '--font-playfair',
})

export const metadata: Metadata = {
  metadataBase: new URL('https://bestlyfefashion.com'),
  title: 'Best Lyfe Fashion - High-End Replica Luxury Fashion',
  description: 'Discover high-end replica luxury fashion from the world\'s most prestigious brands. Premium quality designer replicas at affordable prices.',
  keywords: 'luxury fashion, high-end replica, designer brands, fashion, style, luxury bags, shoes, clothing',
  authors: [{ name: 'Best Lyfe Fashion' }],
  openGraph: {
    title: 'BestlyFe Fashion - Moda Moderna y Elegante',
    description: 'Descubre las últimas tendencias en moda. Ropa elegante y moderna para cada ocasión.',
    type: 'website',
    locale: 'es_ES',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="es" className={`${inter.variable} ${playfair.variable}`}>
      <body className="font-modern antialiased">
        <div className="min-h-screen bg-gradient-to-br from-white to-pink-50">
          {children}
        </div>
      </body>
    </html>
  )
}

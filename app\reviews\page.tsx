'use client'

import { useState } from 'react'
import Header from "@/components/header"
import Footer from "@/components/footer"
import { useReviews } from "@/hooks/use-reviews"

export default function ReviewsPage() {
  const { reviews, isLoading, addReview, getAverageRating, getTotalReviews } = useReviews()
  const [formData, setFormData] = useState({
    rating: 0,
    title: '',
    review: '',
    name: '',
    email: '',
    isGenuine: false
  })
  const [expandedReviews, setExpandedReviews] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleStarClick = (rating: number) => {
    setFormData(prev => ({ ...prev, rating }))
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({ ...prev, [name]: checked }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Add the review using our hook
      const newReview = await addReview({
        name: formData.name,
        title: formData.title,
        rating: formData.rating,
        review: formData.review,
        email: formData.email
      })

      // Show success message
      alert('Thank you for your review! Your review has been published successfully.')

      // Reset form
      setFormData({
        rating: 0,
        title: '',
        review: '',
        name: '',
        email: '',
        isGenuine: false
      })

      console.log('Review saved:', newReview)
    } catch (error) {
      console.error('Error saving review:', error)
      alert('Sorry, there was an error saving your review. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const toggleExpanded = (reviewId: string) => {
    setExpandedReviews(prev =>
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    )
  }

  const renderStars = (rating: number, interactive: boolean = false) => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={interactive ? () => handleStarClick(star) : undefined}
            className={`text-2xl ${interactive ? 'cursor-pointer hover:scale-110 transition-transform' : 'cursor-default'} ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300'
            }`}
            disabled={!interactive}
          >
            ★
          </button>
        ))}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-white">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              Customer
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 ml-4">
                Reviews
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              See what our customers say about our high-end replica fashion collection
            </p>

            {/* Review Statistics */}
            {!isLoading && (
              <div className="flex items-center justify-center gap-8 mb-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">{getAverageRating()}</div>
                  <div className="flex justify-center mb-1">
                    {renderStars(Math.round(getAverageRating()))}
                  </div>
                  <div className="text-sm text-gray-600">Average Rating</div>
                </div>
                <div className="w-px h-12 bg-gray-300"></div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">{getTotalReviews()}</div>
                  <div className="text-sm text-gray-600">Total Reviews</div>
                </div>
              </div>
            )}

            <div className="w-24 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto rounded-full"></div>
          </div>
        </div>
      </section>

      {/* Review Form Section */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            If you like our products please make your review
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Rating */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your overall rating
              </label>
              {renderStars(formData.rating, true)}
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Title of your review
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Summarize your review or highlight an interesting detail"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none transition-colors text-gray-900 placeholder-gray-500"
                required
              />
            </div>

            {/* Review */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your review
              </label>
              <textarea
                name="review"
                value={formData.review}
                onChange={handleInputChange}
                placeholder="Tell people your review"
                rows={6}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none transition-colors resize-vertical text-gray-900 placeholder-gray-500"
                required
              />
            </div>

            {/* Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your name
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Tell us your name"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none transition-colors text-gray-900 placeholder-gray-500"
                required
              />
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your email
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Tell us your email"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none transition-colors text-gray-900 placeholder-gray-500"
                required
              />
            </div>

            {/* Checkbox */}
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                name="isGenuine"
                checked={formData.isGenuine}
                onChange={handleInputChange}
                className="mt-1 w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
                required
              />
              <label className="text-sm text-gray-700">
                This review is based on my own experience and is my genuine opinion.
              </label>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-gray-800 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-900 transition-colors duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isSubmitting ? 'SUBMITTING...' : 'SUBMIT YOUR REVIEW'}
            </button>
          </form>
        </div>
      </section>

      {/* Existing Reviews Section */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="container mx-auto max-w-4xl">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Our latest Reviews
          </h2>

          {isLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
              <p className="text-gray-600 mt-4">Loading reviews...</p>
            </div>
          ) : (
            <div className="space-y-8">
              {reviews.map((review) => (
              <div key={review.id} className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                {/* Review Header */}
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900 mb-1">
                      {review.title}
                    </h3>
                    <div className="flex items-center gap-2 mb-2">
                      {renderStars(review.rating)}
                      <span className="text-sm text-gray-600">{review.date}</span>
                    </div>
                  </div>
                </div>

                {/* Review Content */}
                <div className="mb-4">
                  <p className="text-gray-700 leading-relaxed">
                    {expandedReviews.includes(review.id) ? review.fullReview : review.review}
                  </p>
                  {review.review !== review.fullReview && (
                    <button
                      onClick={() => toggleExpanded(review.id)}
                      className="text-orange-600 hover:text-orange-700 text-sm font-medium mt-2 transition-colors"
                    >
                      {expandedReviews.includes(review.id) ? 'Show less' : 'Show more'}
                    </button>
                  )}
                </div>

                {/* Reviewer Name */}
                <div className="text-sm text-gray-600 font-medium">
                  {review.name}
                </div>
              </div>
              ))}

              {reviews.length === 0 && (
                <div className="text-center py-12">
                  <p className="text-gray-600 text-lg">No reviews yet. Be the first to leave a review!</p>
                </div>
              )}
            </div>
          )}

          {/* Load More Button */}
          <div className="text-center mt-12">
            <button className="bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200 transform hover:scale-105">
              Load More Reviews
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

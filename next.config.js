/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['images.unsplash.com', 'via.placeholder.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      }
    ],
  },
  // Optimización para production
  compress: true,
  poweredByHeader: false,
  // Configuración para BanaHosting
  trailingSlash: false,
  output: 'standalone'
}

module.exports = nextConfig

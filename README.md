# BestlyFe Fashion 👗✨

Una moderna tienda de moda online construida con Next.js y Tailwind CSS.

## 🚀 Características

- ✅ Diseño moderno y responsivo
- ✅ Catálogo de productos de moda
- ✅ Carrito de compras
- ✅ Optimizado para SEO
- ✅ Performance optimizada

## 🛠️ Tecnologías

- **Next.js 14** - Framework React con SSR
- **TypeScript** - Tipado estático
- **Tailwind CSS** - Estilos utilitarios
- **React** - Biblioteca de UI

## 📦 Instalación

```bash
# Instalar dependencias
npm install

# Ejecutar en desarrollo
npm run dev

# Construir para producción
npm run build
```

## 🎨 Desarrollo

El proyecto está configurado con:
- Hot reload para desarrollo rápido
- TypeScript para mejor experiencia de desarrollo
- Tailwind CSS para estilos rápidos y consistentes
- ESLint para calidad de código

## 📱 Estructura del Proyecto

```
bestlyfefashion/
├── app/                 # App Router (Next.js 13+)
├── components/          # Componentes reutilizables
├── public/             # Archivos estáticos
├── styles/             # Estilos globales
└── types/              # Definiciones de TypeScript
```

---

Desarrollado con ❤️ para la moda moderna

'use client'

import { useState, useMemo, useCallback } from 'react'
import Header from "@/components/header"
import Footer from "@/components/footer"
import Image from "next/image"

// ✅ Optimización: Array estático fuera del componente
const LUXURY_BAG_BRANDS = [
  {
    id: 'louis-vuitton',
    name: 'LOUIS VUITTON',
    logo: '/images/catalog/men/bags/brands/Louis-vuitton.png',
    productImage: '/images/catalog/men/bags/brands/flip/Louis-vuitton.jpg',
    description: 'Iconic luxury leather goods and timeless elegance',
    link: 'https://a202103152106152090001782.szwego.com/static/index.html?t=1701963945108#/goods_list/A202103152106152090001782?tagId=19489138'
  },
  {
    id: 'dior',
    name: 'Dior',
    logo: '/images/catalog/men/bags/brands/Dior-bags.png',
    productImage: '/images/catalog/men/bags/brands/flip/Dior-bags.jpg',
    description: 'French luxury and sophisticated craftsmanship',
    link: 'https://a202103152106152090001782.szwego.com/static/index.html?t=1701964008858#/goods_list/A202103152106152090001782?tagId=20023379'
  },
  {
    id: 'gucci',
    name: 'GUCCI',
    logo: '/images/catalog/men/bags/brands/Gucci-bags.png',
    productImage: '/images/catalog/men/bags/brands/flip/gucci.jpg',
    description: 'Italian luxury fashion and innovative design',
    link: 'https://a202103152106152090001782.szwego.com/static/index.html?t=1701964063261#/goods_list/A202103152106152090001782?tagId=19489103'
  },
  {
    id: 'fendi',
    name: 'FENDI',
    logo: '/images/catalog/men/bags/brands/fendi-logo.jpg',
    productImage: '/images/catalog/men/bags/brands/flip/Fendi.jpg',
    description: 'Roman luxury house known for exceptional leather goods',
    link: 'https://a202103152106152090001782.szwego.com/static/index.html?t=1701964124844#/goods_list/A202103152106152090001782?tagId=19489066'
  },
  {
    id: 'hermes',
    name: 'HERMÈS',
    logo: '/images/catalog/men/bags/brands/Hermes-Logo-300x169.png',
    productImage: '/images/catalog/men/bags/brands/flip/Hermes.jpg',
    description: 'Parisian luxury and unparalleled craftsmanship',
    link: 'https://a202103152106152090001782.szwego.com/static/index.html?t=1701964163013#/goods_list/A202103152106152090001782?tagId=19488998'
  },
  {
    id: 'prada',
    name: 'PRADA',
    logo: '/images/catalog/men/bags/brands/prada-logo-milano-1-300x169.png',
    productImage: '/images/catalog/men/bags/brands/flip/prada.jpg',
    description: 'Milano luxury fashion since 1913',
    link: 'https://a202103152106152090001782.szwego.com/static/index.html?t=1701964200923#/goods_list/A202103152106152090001782?tagId=19459948'
  }
] as const

export default function MenBagsPage() {
  const [selectedBrand, setSelectedBrand] = useState<string | null>(null)
  const [isSmallView, setIsSmallView] = useState(false)

  // ✅ Optimización: useCallback para evitar re-creación de función
  const handleBrandClick = useCallback((brandId: string) => {
    setSelectedBrand(brandId)
    const brand = LUXURY_BAG_BRANDS.find(b => b.id === brandId)
    if (brand?.link) {
      // Navigate to the brand's product catalog in the same window
      window.location.href = brand.link
    }
  }, [])

  // ✅ Optimización: useCallback para toggle de vista
  const toggleView = useCallback(() => {
    setIsSmallView(prev => !prev)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-16 px-4 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 w-32 h-32 bg-gray-900 rounded-full blur-3xl"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-gray-700 rounded-full blur-2xl"></div>
          <div className="absolute bottom-20 left-1/3 w-40 h-40 bg-gray-800 rounded-full blur-3xl"></div>
        </div>
        
        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="text-center mb-4 relative">
            {/* Filter Button - Top Right */}
            <div className="absolute top-0 right-0 sm:relative sm:top-auto sm:right-auto sm:flex sm:justify-end sm:mb-4">
              <button
                onClick={toggleView}
                className="bg-orange-600 text-white p-2 sm:p-3 rounded-lg hover:bg-orange-700 transition-colors duration-200 shadow-lg flex items-center gap-2 text-xs sm:text-sm"
                title={isSmallView ? "Switch to Large View" : "Switch to Small View"}
              >
                {isSmallView ? (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                )}
              </button>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-4">
              Choose your
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 ml-4">
                Brands
              </span>
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto mt-4 rounded-full"></div>
          </div>
        </div>
      </section>

      {/* Brands Grid */}
      <section className="py-4 px-4 bg-white">
        <div className="container mx-auto max-w-6xl">
          <div className={`grid ${
            isSmallView
              ? 'grid-cols-3 gap-4'
              : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
          }`}>
            {LUXURY_BAG_BRANDS.map((brand) => (
              <div
                key={brand.id}
                onClick={() => handleBrandClick(brand.id)}
                className={`group relative cursor-pointer transition-all duration-500 overflow-hidden ${
                  selectedBrand === brand.id ? 'scale-105' : 'hover:scale-105'
                }`}
              >
                {/* Brand Card Container */}
                <div className={`relative bg-white shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100 group-hover:border-gray-200 overflow-hidden ${
                  isSmallView
                    ? 'rounded-xl h-48 p-4'
                    : 'rounded-3xl h-80 p-12'
                }`}>

                  {/* Front Side - Brand Logo (Default) */}
                  <div className={`absolute inset-0 w-full h-full bg-white flex flex-col transition-opacity duration-500 group-hover:opacity-0 ${
                    isSmallView ? 'rounded-xl p-4' : 'rounded-3xl p-12'
                  }`}>
                    {/* Brand Logo Area */}
                    <div className={`flex items-center justify-center flex-shrink-0 ${
                      isSmallView ? 'h-16 mb-2' : 'h-32 mb-8'
                    }`}>
                      <div className="relative w-full h-full flex items-center justify-center">
                        <Image
                          src={brand.logo}
                          alt={`${brand.name} logo`}
                          width={isSmallView ? 120 : 200}
                          height={isSmallView ? 60 : 120}
                          className="object-contain max-w-full max-h-full"
                          style={{
                            maxWidth: isSmallView ? '100px' : '180px',
                            maxHeight: isSmallView ? '50px' : '100px'
                          }}
                        />
                      </div>
                    </div>

                    {/* Brand Description */}
                    {!isSmallView && (
                      <div className="text-center flex-grow flex flex-col justify-between">
                        <p className="text-gray-600 text-sm leading-relaxed mb-4">
                          {brand.description}
                        </p>
                        <div className="flex items-center justify-center gap-2 text-orange-600 text-xs font-medium mt-auto">
                          <span>Hover to Preview</span>
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </div>
                      </div>
                    )}

                    {/* Small View Brand Name */}
                    {isSmallView && (
                      <div className="text-center mt-auto">
                        <h3 className="text-xs font-bold text-gray-900 truncate">{brand.name}</h3>
                      </div>
                    )}
                  </div>

                  {/* Back Side - Product Image (On Hover) */}
                  <div className={`absolute inset-0 w-full h-full bg-white overflow-hidden opacity-0 group-hover:opacity-100 transition-opacity duration-500 ${
                    isSmallView ? 'rounded-xl' : 'rounded-3xl'
                  }`}>
                    <div className="relative w-full h-full">
                      <Image
                        src={brand.productImage}
                        alt={`${brand.name} products`}
                        fill
                        className="object-cover"
                      />
                      {/* Overlay with brand name */}
                      <div className={`absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent flex items-end justify-center ${
                        isSmallView ? 'p-2' : 'p-6'
                      }`}>
                        <div className="text-center">
                          <h3 className={`text-white font-bold mb-1 ${
                            isSmallView ? 'text-xs' : 'text-2xl mb-2'
                          }`}>{brand.name}</h3>
                          {!isSmallView && (
                            <div className="flex items-center justify-center gap-2 text-orange-400 text-sm font-medium">
                              <span>Click to View Collection</span>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Selection Indicator */}
                {selectedBrand === brand.id && (
                  <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center shadow-lg">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 px-4 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="container mx-auto max-w-4xl text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Explore Premium Collections?
          </h2>
          <p className="text-xl text-gray-300 mb-8 leading-relaxed">
            Each brand represents the pinnacle of luxury craftsmanship. Discover bags that define sophistication and elevate your style.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-gray-900 px-8 py-4 rounded-xl hover:bg-gray-100 transition-colors font-bold text-lg shadow-lg">
              View All Collections
            </button>
            <button className="border-2 border-white text-white px-8 py-4 rounded-xl hover:bg-white hover:text-gray-900 transition-colors font-bold text-lg">
              Contact Personal Shopper
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"
import { useState, useCallback } from "react"

export default function Categories() {
  const [hoveredSide, setHoveredSide] = useState<string | null>(null)

  // ✅ Optimización: useCallback para handlers de hover
  const handleMouseEnter = useCallback((side: string) => {
    setHoveredSide(side)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setHoveredSide(null)
  }, [])

  return (
    <>

      {/* Section Header - Outside the images */}
      <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Shop by
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 ml-3">
                Category
              </span>
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Discover high-end replica luxury fashion curated for the modern fashion enthusiast
            </p>
            <div className="w-20 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto mt-4 rounded-full"></div>
          </div>
        </div>
      </section>

      {/* Split Screen Images */}
      <section className="relative h-screen overflow-hidden">
        {/* Animated Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black opacity-20 z-10"></div>

      {/* Floating Particles */}
      <div className="absolute inset-0 z-5">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white/20 rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-white/30 rounded-full animate-ping"></div>
        <div className="absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-white/25 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-white/20 rounded-full animate-ping delay-500"></div>
      </div>

      {/* Split Screen Container */}
      <div className="relative h-full flex">
        {/* Women's Side */}
        <Link href="/women" className="block flex-1 relative z-30">
          <div
            className={`relative h-full group cursor-pointer transition-all duration-1000 ease-out ${
              hoveredSide === 'women' ? 'flex-[1.2]' : hoveredSide === 'men' ? 'flex-[0.8]' : 'flex-1'
            }`}
            onMouseEnter={() => handleMouseEnter('women')}
            onMouseLeave={handleMouseLeave}
            onClick={() => console.log('Women side clicked!')}
          >
            {/* Background Image with Effects */}
            <Image
              src="/images/women-homepage.jpg"
              alt="Women's luxury fashion collection"
              fill
              className={`object-cover transition-all duration-1000 ease-out ${
                hoveredSide === 'women' ? 'scale-110 brightness-110 saturate-110' : 'scale-105 brightness-95 saturate-90'
              }`}
              priority
            />

            {/* Dynamic Overlay with Animation */}
            <div className={`absolute inset-0 transition-all duration-700 ease-out ${
              hoveredSide === 'women'
                ? 'bg-gradient-to-r from-pink-500/50 via-rose-400/40 to-purple-500/30'
                : hoveredSide === 'men'
                ? 'bg-black/70'
                : 'bg-gradient-to-r from-pink-600/30 via-rose-500/20 to-transparent'
            }`}></div>

            {/* Animated Glow Effect */}
            <div className={`absolute inset-0 transition-all duration-1000 ease-out ${
              hoveredSide === 'women' ? 'opacity-100 animate-pulse' : 'opacity-0'
            }`}>
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-pink-300/30 to-transparent"></div>
            </div>

            {/* Floating Elements */}
            <div className={`absolute top-12 right-12 transition-all duration-700 ${
              hoveredSide === 'women' ? 'opacity-100 scale-125 animate-bounce' : 'opacity-60 scale-100'
            }`}>
              <div className="w-6 h-6 bg-pink-400/80 rounded-full shadow-lg shadow-pink-400/50 animate-pulse"></div>
            </div>
            <div className={`absolute top-24 right-24 transition-all duration-700 delay-300 ${
              hoveredSide === 'women' ? 'opacity-100 scale-125 animate-bounce' : 'opacity-40 scale-100'
            }`}>
              <div className="w-4 h-4 bg-rose-400/90 rounded-full shadow-lg shadow-rose-400/50 animate-ping"></div>
            </div>
            <div className={`absolute top-36 right-8 transition-all duration-700 delay-500 ${
              hoveredSide === 'women' ? 'opacity-100 scale-125 animate-bounce' : 'opacity-30 scale-100'
            }`}>
              <div className="w-3 h-3 bg-purple-400/70 rounded-full shadow-lg shadow-purple-400/50 animate-pulse"></div>
            </div>

            {/* Main Content */}
            <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 md:p-8 lg:p-12">
              <div className={`transform transition-all duration-700 ${
                hoveredSide === 'women' ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-90'
              }`}>
                <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-4 sm:mb-6 md:mb-8 tracking-wider">
                  WOMEN
                </h2>

                <Button className="bg-white text-gray-900 hover:bg-gray-100 font-bold px-4 py-2 sm:px-6 sm:py-3 md:px-8 md:py-4 rounded-xl md:rounded-2xl shadow-2xl transition-all duration-300 hover:shadow-3xl hover:scale-105 text-sm sm:text-base md:text-lg">
                  Explore Collections
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2 sm:ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
        </Link>

        {/* Men's Side */}
        <Link href="/men" className="block flex-1 relative z-30">
          <div
            className={`relative h-full group cursor-pointer transition-all duration-1000 ease-out ${
              hoveredSide === 'men' ? 'flex-[1.2]' : hoveredSide === 'women' ? 'flex-[0.8]' : 'flex-1'
            }`}
            onMouseEnter={() => handleMouseEnter('men')}
            onMouseLeave={handleMouseLeave}
            onClick={() => console.log('Men side clicked!')}
          >
            {/* Background Image with Effects */}
            <Image
              src="/images/men-homepage.jpg"
              alt="Men's luxury fashion collection"
              fill
              className={`object-cover transition-all duration-1000 ease-out ${
                hoveredSide === 'men' ? 'scale-110 brightness-110 saturate-110' : 'scale-105 brightness-95 saturate-90'
              }`}
              priority
            />

            {/* Dynamic Overlay with Animation */}
            <div className={`absolute inset-0 transition-all duration-700 ease-out ${
              hoveredSide === 'men'
                ? 'bg-gradient-to-l from-blue-500/50 via-indigo-400/40 to-cyan-500/30'
                : hoveredSide === 'women'
                ? 'bg-black/70'
                : 'bg-gradient-to-l from-blue-600/30 via-indigo-500/20 to-transparent'
            }`}></div>

            {/* Animated Glow Effect */}
            <div className={`absolute inset-0 transition-all duration-1000 ease-out ${
              hoveredSide === 'men' ? 'opacity-100 animate-pulse' : 'opacity-0'
            }`}>
              <div className="absolute inset-0 bg-gradient-to-l from-transparent via-blue-300/30 to-transparent"></div>
            </div>

            {/* Floating Elements */}
            <div className={`absolute top-12 left-12 transition-all duration-700 ${
              hoveredSide === 'men' ? 'opacity-100 scale-125 animate-bounce' : 'opacity-60 scale-100'
            }`}>
              <div className="w-6 h-6 bg-blue-400/80 rounded-full shadow-lg shadow-blue-400/50 animate-pulse"></div>
            </div>
            <div className={`absolute top-24 left-24 transition-all duration-700 delay-300 ${
              hoveredSide === 'men' ? 'opacity-100 scale-125 animate-bounce' : 'opacity-40 scale-100'
            }`}>
              <div className="w-4 h-4 bg-indigo-400/90 rounded-full shadow-lg shadow-indigo-400/50 animate-ping"></div>
            </div>
            <div className={`absolute top-36 left-8 transition-all duration-700 delay-500 ${
              hoveredSide === 'men' ? 'opacity-100 scale-125 animate-bounce' : 'opacity-30 scale-100'
            }`}>
              <div className="w-3 h-3 bg-cyan-400/70 rounded-full shadow-lg shadow-cyan-400/50 animate-pulse"></div>
            </div>

            {/* Main Content */}
            <div className="absolute bottom-0 right-0 left-0 p-4 sm:p-6 md:p-8 lg:p-12 text-right">
              <div className={`transform transition-all duration-700 ${
                hoveredSide === 'men' ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-90'
              }`}>
                <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-4 sm:mb-6 md:mb-8 tracking-wider">
                  MEN
                </h2>

                <Button className="bg-white text-gray-900 hover:bg-gray-100 font-bold px-4 py-2 sm:px-6 sm:py-3 md:px-8 md:py-4 rounded-xl md:rounded-2xl shadow-2xl transition-all duration-300 hover:shadow-3xl hover:scale-105 text-sm sm:text-base md:text-lg">
                  Explore Collections
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 ml-2 sm:ml-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Button>
              </div>
            </div>
          </div>
        </Link>
      </div>

        {/* Center Divider Line with Glow Effect */}
        <div className="absolute top-0 bottom-0 left-1/2 transform -translate-x-1/2 w-px bg-gradient-to-b from-transparent via-white/40 to-transparent z-20 shadow-lg shadow-white/20"></div>
      </section>
    </>
  )
}

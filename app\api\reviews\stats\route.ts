import { NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb'
import Review from '@/models/Review'

// GET - Fetch review statistics
export async function GET() {
  try {
    await connectDB()

    // Use Mongoose aggregation for better performance
    const stats = await Review.aggregate([
      { $match: { isApproved: true } },
      {
        $group: {
          _id: null,
          totalReviews: { $sum: 1 },
          averageRating: { $avg: '$rating' },
          ratings: { $push: '$rating' }
        }
      }
    ])

    if (stats.length === 0) {
      return NextResponse.json({
        success: true,
        stats: {
          totalReviews: 0,
          averageRating: 0,
          ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        }
      })
    }

    const { totalReviews, averageRating, ratings } = stats[0]

    // Calculate rating distribution
    const ratingDistribution = {
      1: 0,
      2: 0,
      3: 0,
      4: 0,
      5: 0
    }

    ratings.forEach((rating: number) => {
      ratingDistribution[rating as keyof typeof ratingDistribution]++
    })

    return NextResponse.json({
      success: true,
      stats: {
        totalReviews,
        averageRating: Math.round(averageRating * 10) / 10,
        ratingDistribution
      }
    })
  } catch (error) {
    console.error('Error fetching review stats:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch review statistics' },
      { status: 500 }
    )
  }
}

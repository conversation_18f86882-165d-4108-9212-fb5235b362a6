"use client"

import Link from "next/link"
import { Phone, Mail, Menu, X } from "lucide-react"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useState } from "react"

export default function Header() {
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  return (
    <>
      {/* Top Bar */}
      <div className="bg-gradient-to-r from-orange-300 via-orange-400 to-orange-500 px-4 py-2 text-xs">
        <div className="container mx-auto flex flex-col lg:flex-row justify-between items-center gap-2">
          <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-2">
              <Phone className="w-3 h-3" />
              <span className="text-center sm:text-left">Store: DR ************** / USA **************</span>
            </div>
            <div className="flex items-center gap-2">
              <Mail className="w-3 h-3" />
              <span>Email: <EMAIL></span>
            </div>
            <div className="flex items-center gap-2">
              <svg className="w-3 h-3 text-gray-600 hover:text-pink-500 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
              </svg>
              <Link
                href="https://www.instagram.com/bestlyfefashionrd?igsh=MW5ibmE5OTg4NHAxbg=="
                target="_blank"
                rel="noopener noreferrer"
                className="hover:bg-gradient-to-tr hover:from-yellow-400 hover:via-pink-500 hover:to-purple-600 hover:bg-clip-text hover:text-transparent transition-all duration-300"
              >
                @bestlyfefashionRD
              </Link>
            </div>
          </div>
          <div className="hidden lg:flex gap-4 text-xs">
            <Link href="#" className="hover:underline">
              WELCOME TO OUR STORE
            </Link>
            <Link href="#" className="hover:underline">
              ABOUT US
            </Link>
            <Link href="/contact" className="hover:underline">
              CONTACT
            </Link>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between w-full">
            {/* Logo Section */}
            <div className="flex items-center gap-3">
              <Image
                src="/images/logo-new.png"
                alt="Best Lyfe Fashion Logo"
                width={40}
                height={40}
                className="object-contain"
              />
              <div className="hidden sm:block">
                <h1 className="text-lg font-bold text-black">Best Lyfe Fashion</h1>
                <p className="text-xs text-gray-600">Luxury Fashion Store</p>
              </div>
              <div className="sm:hidden">
                <h1 className="text-sm font-bold text-black">Best Lyfe Fashion</h1>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center gap-8">
              <Link href="/" className={`hover:text-orange-600 font-bold text-sm ${pathname === '/' || pathname === '/collection' ? 'text-orange-600' : 'text-gray-900'}`}>
                HOME
              </Link>
              <Link href="/women" className={`hover:text-orange-600 font-bold text-sm ${pathname === '/women' ? 'text-orange-600' : 'text-gray-900'}`}>
                WOMEN
              </Link>
              <Link href="/men" className={`hover:text-orange-600 font-bold text-sm ${pathname === '/men' ? 'text-orange-600' : 'text-gray-900'}`}>
                MEN
              </Link>
              <Link href="/about" className={`hover:text-orange-600 font-bold text-sm ${pathname === '/about' ? 'text-orange-600' : 'text-gray-900'}`}>
                ABOUT
              </Link>
              <Link href="/contact" className={`hover:text-orange-600 font-bold text-sm ${pathname === '/contact' ? 'text-orange-600' : 'text-gray-900'}`}>
                CONTACT
              </Link>
              <Link href="/videos" className={`hover:text-orange-600 font-bold text-sm ${pathname === '/videos' ? 'text-orange-600' : 'text-gray-900'}`}>
                VIDEOS
              </Link>
              <Link href="/reviews" className={`hover:text-orange-600 font-bold text-sm ${pathname === '/reviews' ? 'text-orange-600' : 'text-gray-900'}`}>
                REVIEWS
              </Link>
              <Link href="/how-to-order" className={`bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-bold text-sm transition-colors ${pathname === '/how-to-order' ? 'bg-orange-600' : ''}`}>
                HOW TO ORDER
              </Link>
            </nav>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden p-2 rounded-md hover:bg-gray-100 transition-colors"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6 text-gray-900" />
              ) : (
                <Menu className="w-6 h-6 text-gray-900" />
              )}
            </button>
          </div>

          {/* Mobile Navigation */}
          {isMobileMenuOpen && (
            <div className="lg:hidden mt-4 pb-4 border-t border-gray-200">
              <nav className="flex flex-col gap-4 pt-4">
                <Link
                  href="/"
                  className={`hover:text-orange-600 font-bold text-sm ${pathname === '/' || pathname === '/collection' ? 'text-orange-600' : 'text-gray-900'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  HOME
                </Link>
                <Link
                  href="/women"
                  className={`hover:text-orange-600 font-bold text-sm ${pathname === '/women' ? 'text-orange-600' : 'text-gray-900'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  WOMEN
                </Link>
                <Link
                  href="/men"
                  className={`hover:text-orange-600 font-bold text-sm ${pathname === '/men' ? 'text-orange-600' : 'text-gray-900'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  MEN
                </Link>
                <Link
                  href="/about"
                  className={`hover:text-orange-600 font-bold text-sm ${pathname === '/about' ? 'text-orange-600' : 'text-gray-900'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  ABOUT
                </Link>
                <Link
                  href="/contact"
                  className={`hover:text-orange-600 font-bold text-sm ${pathname === '/contact' ? 'text-orange-600' : 'text-gray-900'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  CONTACT
                </Link>
                <Link
                  href="/videos"
                  className={`hover:text-orange-600 font-bold text-sm ${pathname === '/videos' ? 'text-orange-600' : 'text-gray-900'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  VIDEOS
                </Link>
                <Link
                  href="/reviews"
                  className={`hover:text-orange-600 font-bold text-sm ${pathname === '/reviews' ? 'text-orange-600' : 'text-gray-900'}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  REVIEWS
                </Link>
                <Link
                  href="/how-to-order"
                  className={`bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-bold text-sm transition-colors text-center ${pathname === '/how-to-order' ? 'bg-orange-600' : ''}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  HOW TO ORDER
                </Link>
              </nav>
            </div>
          )}
        </div>
      </header>
    </>
  )
}

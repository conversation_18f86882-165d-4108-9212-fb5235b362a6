'use client'

import { useState, useEffect } from 'react'

export interface Review {
  id: string
  name: string
  title: string
  rating: number
  date: string
  review: string
  fullReview: string
  email: string
  isApproved: boolean
}

export interface ReviewStats {
  totalReviews: number
  averageRating: number
  ratingDistribution: {
    1: number
    2: number
    3: number
    4: number
    5: number
  }
}



export function useReviews() {
  const [reviews, setReviews] = useState<Review[]>([])
  const [stats, setStats] = useState<ReviewStats>({
    totalReviews: 0,
    averageRating: 0,
    ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
  })
  const [isLoading, setIsLoading] = useState(true)

  // Load reviews from API on component mount
  useEffect(() => {
    fetchReviews()
    fetchStats()
  }, [])

  // Calculate stats from reviews array
  const calculateStats = (reviewsArray: Review[]) => {
    if (reviewsArray.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      }
    }

    const totalReviews = reviewsArray.length
    const averageRating = reviewsArray.reduce((sum, review) => sum + review.rating, 0) / totalReviews
    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }

    reviewsArray.forEach(review => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++
    })

    return { totalReviews, averageRating, ratingDistribution }
  }

  // Fetch reviews from API
  const fetchReviews = async () => {
    try {
      const response = await fetch('/api/reviews')
      const data = await response.json()

      if (data.success) {
        setReviews(data.reviews)
      } else {
        console.error('Error fetching reviews:', data.error)
        // Fallback to localStorage
        const localReviews = JSON.parse(localStorage.getItem('reviews') || '[]')
        setReviews(localReviews)
      }
    } catch (error) {
      console.error('Error fetching reviews, using localStorage:', error)
      // Fallback to localStorage
      const localReviews = JSON.parse(localStorage.getItem('reviews') || '[]')
      setReviews(localReviews)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch review statistics from API
  const fetchStats = async () => {
    try {
      const response = await fetch('/api/reviews/stats')
      const data = await response.json()

      if (data.success) {
        setStats(data.stats)
      } else {
        console.error('Error fetching stats:', data.error)
        // Fallback to localStorage
        const localReviews = JSON.parse(localStorage.getItem('reviews') || '[]')
        const localStats = calculateStats(localReviews)
        setStats(localStats)
      }
    } catch (error) {
      console.error('Error fetching stats, using localStorage:', error)
      // Fallback to localStorage
      const localReviews = JSON.parse(localStorage.getItem('reviews') || '[]')
      const localStats = calculateStats(localReviews)
      setStats(localStats)
    }
  }

  // Add a new review
  const addReview = async (reviewData: {
    name: string
    title: string
    rating: number
    review: string
    email: string
  }) => {
    try {
      // Try API first
      const response = await fetch('/api/reviews', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reviewData),
      })

      const data = await response.json()

      if (data.success) {
        // Add the new review to the local state
        setReviews(prev => [data.review, ...prev])
        // Refresh stats
        await fetchStats()
        return data.review
      } else {
        throw new Error(data.error || 'Failed to create review')
      }
    } catch (error) {
      console.error('Error adding review via API, using localStorage fallback:', error)

      // Fallback to localStorage
      const newReview = {
        id: Date.now().toString(),
        name: reviewData.name,
        title: reviewData.title,
        rating: reviewData.rating,
        review: reviewData.review,
        fullReview: reviewData.review,
        email: reviewData.email,
        date: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }),
        isApproved: true
      }

      // Save to localStorage
      const existingReviews = JSON.parse(localStorage.getItem('reviews') || '[]')
      const updatedReviews = [newReview, ...existingReviews]
      localStorage.setItem('reviews', JSON.stringify(updatedReviews))

      // Update local state
      setReviews(prev => [newReview, ...prev])

      // Update stats
      const newStats = calculateStats(updatedReviews)
      setStats(newStats)

      return newReview
    }
  }

  // Get average rating from stats
  const getAverageRating = () => {
    return stats.averageRating
  }

  // Get total review count from stats
  const getTotalReviews = () => {
    return stats.totalReviews
  }

  return {
    reviews,
    stats,
    isLoading,
    addReview,
    getAverageRating,
    getTotalReviews,
    refreshReviews: fetchReviews,
    refreshStats: fetchStats
  }
}

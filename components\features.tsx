import { Search, Star, Heart } from "lucide-react"

// ✅ Optimización: Array estático fuera del componente
const FEATURES = [
  {
    icon: Search,
    title: "Easy Search",
    description: "Find your perfect style quickly",
  },
  {
    icon: Star,
    title: "Premium Quality",
    description: "High-end replica luxury brands guaranteed",
  },
  {
    icon: Heart,
    title: "Customer Care",
    description: "24/7 customer support available",
  },
] as const

export default function Features() {

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-3 gap-12">
          {FEATURES.map((feature, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <feature.icon className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

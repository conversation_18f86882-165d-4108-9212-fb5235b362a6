# 🚀 Deployment Guide - Best Lyfe Fashion

## 📋 Deployment to BanaHosting

### 🔧 Prerequisites
- BanaHosting Professional Deluxe Unlimited SSD
- Node.js enabled on hosting
- Domain: bestlyfefashion.com

### 📦 Step 1: Prepare the Build
```bash
# Install dependencies
npm install

# Build for production
npm run build
```

### 📁 Step 2: Upload Files
Upload these files/folders to your hosting:
- `.next/` (build output)
- `public/`
- `package.json`
- `package-lock.json`
- `.env.production` (rename to .env.local on server)
- `next.config.js`
- All other project files

### ⚙️ Step 3: Server Setup
1. Access your BanaHosting cPanel
2. Go to Node.js Selector
3. Create new Node.js app:
   - **Node.js version**: Latest LTS
   - **Application root**: public_html
   - **Application URL**: bestlyfefashion.com
   - **Application startup file**: server.js

### 🗄️ Step 4: Environment Variables
In cPanel Node.js app settings, add:
```
NODE_ENV=production
MONGODB_URI=mongodb+srv://anthonchess:<EMAIL>/bestlyfefashion
NEXTAUTH_URL=https://bestlyfefashion.com
```

### 🚀 Step 5: Install Dependencies & Start
```bash
# In cPanel Terminal or SSH
npm install --production
npm start
```

### 🌐 Step 6: Configure Domain
- Point bestlyfefashion.com to your hosting IP
- Enable SSL certificate in cPanel
- Test the site at https://bestlyfefashion.com

### 🔍 Troubleshooting
- Check Node.js app logs in cPanel
- Verify all environment variables are set
- Ensure MongoDB connection is working
- Check file permissions (755 for folders, 644 for files)

### 📞 Support
- BanaHosting Support: https://banahosting.com/support
- Documentation: Check cPanel Node.js documentation

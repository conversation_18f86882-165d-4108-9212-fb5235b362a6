'use client'

import { useState } from 'react'
import Image from 'next/image'
import Header from "@/components/header"
import Footer from "@/components/footer"

export default function AboutPage() {
  const [imageError, setImageError] = useState(false)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-orange-200 rounded-full opacity-20 blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-orange-300 rounded-full opacity-15 blur-3xl"></div>
        
        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
              About
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 ml-4">
                Us
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover the story behind Best Lyfe Fashion and our passion for bringing luxury fashion within reach.
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto mt-8 rounded-full"></div>
          </div>
        </div>
      </section>

      {/* Main Content Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            
            {/* Image Side */}
            <div className="relative">
              <div className="absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full opacity-10 blur-xl"></div>
              <div className="relative">
                {!imageError ? (
                  <Image
                    src="/images/womanfashion.png"
                    alt="Best Lyfe Fashion - Women's luxury collection"
                    width={600}
                    height={400}
                    className="w-full h-96 object-contain"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-full h-96 bg-gradient-to-br from-orange-100 to-orange-200 flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-20 h-20 bg-orange-400 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                      </div>
                      <p className="text-orange-600 font-semibold">Best Lyfe Fashion</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Content Side */}
            <div className="space-y-8">
              <div className="relative">
                <div className="absolute -top-2 -left-2 w-16 h-16 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full opacity-10 blur-lg"></div>
                <div className="relative">
                  <h2 className="text-4xl font-bold text-gray-900 mb-6">
                    Our <span className="text-orange-500">Passion</span>
                  </h2>
                  <p className="text-lg text-gray-700 leading-relaxed mb-6">
                    Best Lyfe Fashion loves designer bags, shoes, belts, scarfs and luggage. We have been engaged in sharing affordable high-end replicas of exceptional quality to the world.
                  </p>
                  <p className="text-lg text-gray-700 leading-relaxed">
                    We realize the thought of owning an authentic luxury designer handbag, such as Louis Vuitton or Hermès, is just a dream to many bag lovers with price tags reaching thousands.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Quality Section */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            
            {/* Content Side */}
            <div className="space-y-8 order-2 lg:order-1">
              <div className="relative">
                <h2 className="text-4xl font-bold text-gray-900 mb-6">
                  Exceptional <span className="text-orange-500">Quality</span>
                </h2>
                <p className="text-lg text-gray-700 leading-relaxed mb-6">
                  We've seen the cheap knock-offs available at street markets throughout the world, but they don't even look like luxury bags. They are often made with cheap materials and dull metal hardware.
                </p>
                <p className="text-lg text-gray-700 leading-relaxed mb-6">
                  It may be a nightmare to try to find a bag with high quality. At Best Lyfe Fashion, we pride ourselves on replicating authentic designer bags to mirror image quality.
                </p>
                <p className="text-lg text-gray-700 leading-relaxed">
                  The bags we create are meticulously crafted by our skilled artisans, with every detail replicated to match the original bags.
                </p>
              </div>
            </div>

            {/* Features Side */}
            <div className="order-1 lg:order-2">
              <div className="grid grid-cols-1 gap-6">
                
                {/* Feature 1 */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
                  <div className="relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-orange-100">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">Premium Materials</h3>
                        <p className="text-gray-600">High-quality leather and hardware</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Feature 2 */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
                  <div className="relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-orange-100">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">Skilled Artisans</h3>
                        <p className="text-gray-600">Meticulously crafted by experts</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Feature 3 */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl blur opacity-20 group-hover:opacity-30 transition-opacity"></div>
                  <div className="relative bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-orange-100">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center">
                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">Mirror Quality</h3>
                        <p className="text-gray-600">Every detail perfectly replicated</p>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-16 px-4 bg-gradient-to-r from-orange-500 to-orange-600">
        <div className="container mx-auto max-w-4xl text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Experience Luxury Within Reach
          </h2>
          <p className="text-xl text-orange-100 mb-8 leading-relaxed">
            Discover our collection of meticulously crafted high-end replicas that bring designer dreams to reality.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/women"
              className="bg-white text-orange-600 px-8 py-4 rounded-full font-bold hover:bg-orange-50 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105"
            >
              Shop Women's Collection
            </a>
            <a
              href="/men"
              className="bg-transparent border-2 border-white text-white px-8 py-4 rounded-full font-bold hover:bg-white hover:text-orange-600 transition-all duration-300"
            >
              Shop Men's Collection
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

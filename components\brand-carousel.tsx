'use client'

import { useEffect, useState, useMemo, useCallback } from 'react'
import Image from 'next/image'

// ✅ Optimización: Array estático fuera del componente
const LUXURY_BRANDS = [
  { name: '<PERSON>', logo: '/images/catalog/men/bags/brands/Louis-vuitton.png' },
  { name: '<PERSON><PERSON>', logo: '/images/catalog/men/shoes/Dior.png' },
  { name: 'Gucci', logo: '/images/catalog/men/shoes/Gucci.png' },
  { name: 'Her<PERSON><PERSON>', logo: '/images/catalog/men/shoes/Hermes.png' },
  { name: 'Prada', logo: '/images/catalog/men/shoes/prada.png' },
  { name: '<PERSON><PERSON>', logo: '/images/catalog/men/shoes/fendi.jpg' },
  { name: '<PERSON><PERSON>', logo: '/images/catalog/men/shoes/channel.png' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', logo: '/images/catalog/men/shoes/balenciaga.png' },
  { name: 'Versace', logo: '/images/catalog/men/shoes/Versace.png' },
  { name: '<PERSON><PERSON>', logo: '/images/catalog/men/shoes/Valentino.png' },
  { name: '<PERSON><PERSON><PERSON>', logo: '/images/catalog/men/shoes/Burberry.png' },
  { name: 'Bottega Veneta', logo: '/images/catalog/men/shoes/Botega.jpg' },
  { name: 'Dolce & Gabbana', logo: '/images/catalog/men/shoes/Dolce-Gabbana.png' },
  { name: 'Givenchy', logo: '/images/catalog/men/shoes/givenchi.png' },
  { name: 'Alexander McQueen', logo: '/images/catalog/men/shoes/Alexander-McQueen.png' },
  { name: 'Off-White', logo: '/images/catalog/men/shoes/offwhite.png' },
  { name: 'Amiri', logo: '/images/catalog/men/shoes/Amiri.png' },
  { name: 'Balmain', logo: '/images/catalog/men/shoes/Balmain.png' },
  { name: 'Ferragamo', logo: '/images/catalog/men/shoes/ferragamo.png' },
  { name: 'Christian Louboutin', logo: '/images/catalog/men/shoes/Christian.gif' }
] as const

export default function BrandCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0)

  // ✅ Optimización: Memorizar la longitud del array
  const brandsLength = useMemo(() => LUXURY_BRANDS.length, [])

  // ✅ Optimización: useCallback para funciones que se pasan como dependencias
  const nextSlide = useCallback(() => {
    setCurrentIndex((prevIndex) => {
      // Reset to 0 when reaching the end of the first set to create seamless loop
      if (prevIndex >= brandsLength - 1) {
        return 0
      }
      return prevIndex + 1
    })
  }, [brandsLength])

  // ✅ Optimización: Auto-scroll effect
  useEffect(() => {
    const interval = setInterval(nextSlide, 3000)
    return () => clearInterval(interval)
  }, [nextSlide])

  // ✅ Optimización: Memorizar el array extendido
  const extendedBrands = useMemo(() =>
    [...LUXURY_BRANDS, ...LUXURY_BRANDS, ...LUXURY_BRANDS],
    []
  )

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Trusted by
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 ml-3">
              Luxury Brands
            </span>
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover high-end replica luxury fashion from the world's most prestigious fashion houses
          </p>
          <div className="w-20 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto mt-4 rounded-full"></div>
        </div>

        {/* Carousel Container */}
        <div className="relative overflow-hidden">
          {/* Main Carousel */}
          <div className="overflow-hidden">
            <div
              className="flex transition-transform duration-1000 ease-in-out gap-3 sm:gap-4 md:gap-6"
              style={{
                transform: `translateX(-${currentIndex * 140}px)`,
              }}
            >
              {extendedBrands.map((brand, index) => (
                <div
                  key={`${brand.name}-${index}`}
                  className="flex-shrink-0"
                >
                  <div className="group relative w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 transition-all duration-500 transform hover:scale-105 flex items-center justify-center">
                    {/* Brand Logo */}
                    <div className="w-full h-full flex items-center justify-center p-2 sm:p-2 md:p-3 lg:p-3">
                      <Image
                        src={brand.logo}
                        alt={`${brand.name} logo`}
                        width={100}
                        height={70}
                        className="object-contain max-w-full max-h-full filter grayscale group-hover:grayscale-0 transition-all duration-500"
                        style={{
                          maxWidth: '100%',
                          maxHeight: '100%'
                        }}
                      />
                    </div>

                    {/* Brand Name Tooltip */}
                    <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap z-10">
                      {brand.name}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Gradient Overlays for Infinite Effect */}
          <div className="absolute left-0 top-0 bottom-0 w-12 sm:w-16 md:w-20 bg-gradient-to-r from-white to-transparent pointer-events-none z-10"></div>
          <div className="absolute right-0 top-0 bottom-0 w-12 sm:w-16 md:w-20 bg-gradient-to-l from-white to-transparent pointer-events-none z-10"></div>
        </div>

        {/* Navigation Dots */}
        <div className="flex justify-center mt-8 gap-2">
          {LUXURY_BRANDS.slice(0, 8).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                Math.floor(currentIndex / 2.5) === index
                  ? 'bg-orange-500 w-8'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>


      </div>
    </section>
  )
}

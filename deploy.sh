#!/bin/bash

# Deployment script for BanaHosting
echo "🚀 Starting deployment to BanaHosting..."

# Install dependencies
echo "📦 Installing dependencies..."
npm install --production

# Build the application
echo "🔨 Building application..."
npm run build

# Copy environment variables
echo "⚙️ Setting up environment..."
cp .env.production .env.local

echo "✅ Deployment preparation complete!"
echo "📁 Ready to upload to BanaHosting"
echo ""
echo "📋 Next steps:"
echo "1. Upload all files to your hosting via cPanel File Manager or FTP"
echo "2. Install Node.js dependencies on the server"
echo "3. Start the application with: npm start"
echo ""
echo "🌐 Your site will be available at: https://bestlyfefashion.com"

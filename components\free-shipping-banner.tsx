import React from 'react'
import Image from 'next/image'

const FreeShippingBanner = () => {
  return (
    <section className="py-12 bg-white border-b border-gray-100">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-center gap-8 text-center flex-wrap">
          {/* Free Shipping */}
          <div className="flex items-center gap-6">
            <div className="w-20 h-20 bg-gradient-to-r from-orange-400 to-orange-600 rounded-full flex items-center justify-center shadow-lg animate-pulse">
              <Image
                src="/images/truck.svg"
                alt="Delivery truck"
                width={40}
                height={40}
                className="filter brightness-0 invert"
              />
            </div>
            <div className="text-left">
              <div className="flex items-center gap-3">
                <span className="text-3xl font-bold text-gray-900 animate-pulse">FREE SHIPPING</span>
                <span className="bg-red-500 text-white text-lg font-bold px-4 py-2 rounded-lg animate-bounce shadow-lg">WORLD-WIDE</span>
              </div>
              <p className="text-lg text-gray-700 font-medium mt-2">Free shipping on all orders worldwide! 🌍</p>
            </div>
          </div>

          {/* PayPal Accepted */}
          <div className="flex items-center gap-6">
            <div className="w-20 h-20 flex items-center justify-center">
              <Image
                src="/paypal.png"
                alt="PayPal"
                width={80}
                height={80}
                className="object-contain"
              />
            </div>
            <div className="text-left">
              <div className="flex items-center gap-3">
                <span className="text-3xl font-bold text-gray-900">PAYPAL</span>
                <span className="bg-blue-500 text-white text-lg font-bold px-4 py-2 rounded-lg shadow-lg">ACCEPTED</span>
              </div>
              <p className="text-lg text-gray-700 font-medium mt-2">PayPal is world-wide, trusted and very secure 🔒</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FreeShippingBanner

'use client'

import { useState, useCallback, useRef } from 'react'

export default function VideoShowcase() {
  const [isPlaying, setIsPlaying] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  // ✅ Optimización: useCallback para handler de play
  const handlePlay = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.play()
      setIsPlaying(true)
    }
  }, [])

  return (
    <section className="relative w-full bg-black overflow-hidden">
      {/* Video Container */}
      <div className="relative w-full h-[60vh] md:h-[70vh] lg:h-[80vh]">
        <video
          ref={videoRef}
          className="w-full h-full object-cover"
          poster="/images/fashion-showcase-thumbnail.jpg"
          controls={isPlaying}
          onPlay={() => setIsPlaying(true)}
          preload="metadata"
        >
          <source src="/images/fashion-showcase.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Play Button Overlay */}
        {!isPlaying && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
            <button
              onClick={handlePlay}
              className="group relative"
              aria-label="Play video"
            >
              {/* Outer Ring */}
              <div className="w-24 h-24 md:w-32 md:h-32 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border-2 border-white/30 group-hover:bg-white/30 transition-all duration-300 group-hover:scale-110">
                {/* Inner Play Button */}
                <div className="w-16 h-16 md:w-20 md:h-20 bg-white rounded-full flex items-center justify-center shadow-2xl group-hover:bg-orange-500 transition-all duration-300">
                  <svg 
                    className="w-8 h-8 md:w-10 md:h-10 text-gray-900 group-hover:text-white ml-1 transition-colors duration-300" 
                    fill="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
              
              {/* Pulse Animation */}
              <div className="absolute inset-0 w-24 h-24 md:w-32 md:h-32 rounded-full border-2 border-white/50 animate-ping"></div>
            </button>
          </div>
        )}



        {/* Decorative Elements */}
        <div className="absolute top-8 right-8 w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
        <div className="absolute top-16 right-12 w-1 h-1 bg-white rounded-full animate-pulse delay-300"></div>
        <div className="absolute top-12 right-20 w-1.5 h-1.5 bg-orange-300 rounded-full animate-pulse delay-700"></div>
      </div>

      {/* Bottom Accent */}
      <div className="h-1 bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600"></div>
    </section>
  )
}

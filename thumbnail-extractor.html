<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Thumbnail Extractor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        video {
            width: 100%;
            max-width: 600px;
            border-radius: 8px;
            margin: 20px 0;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #e55a2b;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .time-display {
            font-size: 18px;
            margin: 10px 0;
            color: #666;
        }
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #ff6b35;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 Video Thumbnail Extractor</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>The video will load automatically from your fashion showcase</li>
                <li>Use the video controls to find the perfect frame</li>
                <li>Click "Capture Thumbnail" when you find a good moment</li>
                <li>Right-click the captured image and "Save As" to download</li>
                <li>Save it as "fashion-showcase-thumbnail.jpg" in your images folder</li>
            </ol>
        </div>

        <video id="videoPlayer" controls crossorigin="anonymous">
            <source src="./public/images/fashion-showcase.mp4" type="video/mp4">
            <source src="public/images/fashion-showcase.mp4" type="video/mp4">
            <source src="images/fashion-showcase.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>

        <div class="controls">
            <div class="time-display" id="timeDisplay">Current Time: 0:00</div>
            <button onclick="captureFrame()">📸 Capture Thumbnail</button>
            <button onclick="seekToTime(2)">Go to 2s</button>
            <button onclick="seekToTime(5)">Go to 5s</button>
            <button onclick="seekToTime(8)">Go to 8s</button>
            <button onclick="seekToTime(12)">Go to 12s</button>
        </div>

        <canvas id="thumbnailCanvas" width="640" height="360" style="display: none;"></canvas>
        
        <div id="thumbnailResult" style="text-align: center;"></div>
    </div>

    <script>
        const video = document.getElementById('videoPlayer');
        const canvas = document.getElementById('thumbnailCanvas');
        const ctx = canvas.getContext('2d');
        const timeDisplay = document.getElementById('timeDisplay');
        const thumbnailResult = document.getElementById('thumbnailResult');

        // Update time display
        video.addEventListener('timeupdate', function() {
            const currentTime = video.currentTime;
            const minutes = Math.floor(currentTime / 60);
            const seconds = Math.floor(currentTime % 60);
            timeDisplay.textContent = `Current Time: ${minutes}:${seconds.toString().padStart(2, '0')}`;
        });

        function seekToTime(seconds) {
            video.currentTime = seconds;
        }

        function captureFrame() {
            console.log('Capture button clicked');
            console.log('Video ready state:', video.readyState);
            console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight);
            console.log('Video current time:', video.currentTime);

            if (video.readyState < 2) {
                alert('Please wait for the video to load completely. Try playing the video first.');
                return;
            }

            if (video.videoWidth === 0 || video.videoHeight === 0) {
                alert('Video dimensions not available. Please play the video first, then try capturing.');
                return;
            }

            try {
                // Set canvas dimensions to match video
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                console.log('Canvas dimensions set to:', canvas.width, 'x', canvas.height);

                // Draw the current video frame to canvas
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                console.log('Frame drawn to canvas');

                // Convert canvas to image
                const dataURL = canvas.toDataURL('image/jpeg', 0.9);
                console.log('Canvas converted to data URL');

                // Create preview image
                const img = document.createElement('img');
                img.src = dataURL;
                img.style.maxWidth = '100%';
                img.style.borderRadius = '8px';
                img.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';

                // Create download link
                const downloadLink = document.createElement('a');
                downloadLink.href = dataURL;
                downloadLink.download = 'fashion-showcase-thumbnail.jpg';
                downloadLink.textContent = '💾 Download Thumbnail';
                downloadLink.style.display = 'inline-block';
                downloadLink.style.marginTop = '10px';
                downloadLink.style.padding = '10px 20px';
                downloadLink.style.background = '#28a745';
                downloadLink.style.color = 'white';
                downloadLink.style.textDecoration = 'none';
                downloadLink.style.borderRadius = '6px';

                // Add success message
                const successMsg = document.createElement('p');
                successMsg.textContent = '✅ Thumbnail captured successfully! Right-click the image below to save it.';
                successMsg.style.color = '#28a745';
                successMsg.style.fontWeight = 'bold';
                successMsg.style.marginTop = '20px';

                // Clear previous result and add new thumbnail
                thumbnailResult.innerHTML = '';
                thumbnailResult.appendChild(successMsg);
                thumbnailResult.appendChild(img);
                thumbnailResult.appendChild(document.createElement('br'));
                thumbnailResult.appendChild(downloadLink);

                console.log('Thumbnail captured and displayed successfully!');
            } catch (error) {
                console.error('Error capturing frame:', error);
                alert('Error capturing frame: ' + error.message);
            }
        }

        // Video event listeners for debugging
        video.addEventListener('loadstart', function() {
            console.log('Video load started');
        });

        video.addEventListener('loadedmetadata', function() {
            console.log('Video metadata loaded');
            console.log('Video duration:', video.duration);
            console.log('Video dimensions:', video.videoWidth, 'x', video.videoHeight);
        });

        video.addEventListener('loadeddata', function() {
            console.log('Video data loaded');
            video.currentTime = 3; // Start at 3 seconds for a good frame
        });

        video.addEventListener('canplay', function() {
            console.log('Video can start playing');
        });

        video.addEventListener('error', function(e) {
            console.error('Video error:', e);
            console.error('Video error details:', video.error);
        });

        // Add a manual file input as backup
        function addFileInput() {
            const fileInputDiv = document.createElement('div');
            fileInputDiv.innerHTML = `
                <h3>Alternative: Upload Video File</h3>
                <p>If the video doesn't load automatically, you can upload it manually:</p>
                <input type="file" id="videoFile" accept="video/*" style="margin: 10px 0;">
                <button onclick="loadVideoFile()">Load Video</button>
            `;
            fileInputDiv.style.background = '#f8f9fa';
            fileInputDiv.style.padding = '20px';
            fileInputDiv.style.borderRadius = '8px';
            fileInputDiv.style.margin = '20px 0';

            document.querySelector('.container').appendChild(fileInputDiv);
        }

        function loadVideoFile() {
            const fileInput = document.getElementById('videoFile');
            const file = fileInput.files[0];

            if (file) {
                const url = URL.createObjectURL(file);
                video.src = url;
                console.log('Video loaded from file');
            }
        }

        // Add file input after a delay if video doesn't load
        setTimeout(addFileInput, 3000);
    </script>
</body>
</html>

const mongoose = require('mongoose')

const uri = 'mongodb+srv://anthonchess:<EMAIL>/bestlyfefashion'

// Define the Review schema
const ReviewSchema = new mongoose.Schema({
  name: { type: String, required: true, trim: true },
  title: { type: String, required: true, trim: true },
  rating: { type: Number, required: true, min: 1, max: 5 },
  date: { type: String, required: true },
  review: { type: String, required: true, trim: true },
  fullReview: { type: String, required: true, trim: true },
  email: { type: String, required: true, trim: true, lowercase: true },
  isApproved: { type: Boolean, default: true }
}, { timestamps: true })

const Review = mongoose.model('Review', ReviewSchema)

const sampleReviews = [
  {
    name: "S. Sador",
    title: "High Fashion",
    rating: 5,
    date: "April 11, 2024",
    review: "I am very excited to tell you about Best Lyfe Fashion products. I have purchased several handbags and was very I am so excited to tell you about Best Lyfe Fashion products. I am so impressed with the timely delivery and no regrets with quality or price point. You cannot tell the difference. I am...",
    fullReview: "I am very excited to tell you about Best Lyfe Fashion products. I have purchased several handbags and was very I am so excited to tell you about Best Lyfe Fashion products. I am so impressed with the timely delivery and no regrets with quality or price point. You cannot tell the difference. I am extremely satisfied with my purchases and will definitely be ordering more items in the future. The customer service is excellent and they respond quickly to any questions.",
    email: "<EMAIL>",
    isApproved: true,
    createdAt: new Date('2024-04-11'),
    updatedAt: new Date('2024-04-11')
  },
  {
    name: "Shauna",
    title: "Quality is perfection!",
    rating: 5,
    date: "April 3, 2024",
    review: "My family has purchased 3 handbags and they are beautiful pieces. Amazing quality and great prices. So happy to have found this store!",
    fullReview: "My family has purchased 3 handbags and they are beautiful pieces. Amazing quality and great prices. So happy to have found this store!",
    email: "<EMAIL>",
    isApproved: true,
    createdAt: new Date('2024-04-03'),
    updatedAt: new Date('2024-04-03')
  },
  {
    name: "Jon Burton",
    title: "Great products",
    rating: 5,
    date: "March 23, 2024",
    review: "We purchased a few items. The quality is perfect. Can't fault it.",
    fullReview: "We purchased a few items. The quality is perfect. Can't fault it.",
    email: "<EMAIL>",
    isApproved: true,
    createdAt: new Date('2024-03-23'),
    updatedAt: new Date('2024-03-23')
  },
  {
    name: "Yesica",
    title: "Confiables",
    rating: 5,
    date: "March 23, 2024",
    review: "Muy buen servicio, calidad y lo más importante son serios.",
    fullReview: "Muy buen servicio, calidad y lo más importante son serios.",
    email: "<EMAIL>",
    isApproved: true,
    createdAt: new Date('2024-03-23'),
    updatedAt: new Date('2024-03-23')
  }
]

async function seedReviews() {
  try {
    await mongoose.connect(uri)
    console.log('Connected to MongoDB with Mongoose')

    // Check if reviews already exist
    const existingCount = await Review.countDocuments()
    if (existingCount > 0) {
      console.log(`Found ${existingCount} existing reviews. Skipping seed.`)
      return
    }

    // Insert sample reviews using Mongoose
    const result = await Review.insertMany(sampleReviews)
    console.log(`Inserted ${result.length} sample reviews`)

  } catch (error) {
    console.error('Error seeding reviews:', error)
  } finally {
    await mongoose.disconnect()
    console.log('Disconnected from MongoDB')
  }
}

// Run the seed function
seedReviews().catch(console.error)

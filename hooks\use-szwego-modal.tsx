'use client'

import { useState, useCallback } from 'react'

export function useSzwegoModal() {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [redirectUrl, setRedirectUrl] = useState('')

  const handleSzwegoClick = useCallback((e: React.MouseEvent<HTMLAnchorElement>, url: string) => {
    // Check if the URL contains szwego.com
    if (url.includes('szwego.com')) {
      e.preventDefault() // Prevent default link behavior
      setRedirectUrl(url)
      setIsModalOpen(true)
    }
    // If it's not a szwego link, let it proceed normally
  }, [])

  const closeModal = useCallback(() => {
    setIsModalOpen(false)
    setRedirectUrl('')
  }, [])

  return {
    isModalOpen,
    redirectUrl,
    handleSzwegoClick,
    closeModal
  }
}

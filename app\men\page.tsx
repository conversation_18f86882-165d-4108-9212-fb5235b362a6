"use client"

import Link from "next/link"
import Image from "next/image"
import { useState } from "react"
import Header from "@/components/header"
import Footer from "@/components/footer"

const categories = [
  {
    id: 1,
    name: "Men Bags",
    image: "/images/catalog/men/bags/men-bags.jpeg",
    description: "Premium leather bags and briefcases for the modern man",
    gradient: "from-gray-600 to-gray-800",
    href: "/men/bags"
  },
  {
    id: 2,
    name: "Men Belts",
    image: "/images/catalog/men/belts/men belts.jpeg",
    description: "Elegant leather belts for every occasion",
    gradient: "from-amber-600 to-orange-600",
    href: "https://lh-bubaidu.x.yupoo.com"
  },
  {
    id: 3,
    name: "Sunglasses",
    image: "/images/catalog/men/sunglasses/men-sunglasses.jpeg",
    description: "Designer sunglasses for ultimate style",
    gradient: "from-slate-500 to-gray-700",
    href: "https://shangmei168.x.yupoo.com/albums"
  },
  {
    id: 4,
    name: "Travel",
    image: "/images/catalog/men/travel/men-travel.jpeg",
    description: "Travel essentials and luggage for adventures",
    gradient: "from-blue-600 to-indigo-600",
    href: "https://wholesale22.x.yupoo.com/albums/*********?uid=1&isSubCate=false&referrercate=4460021"
  },
  {
    id: 5,
    name: "Men Shoes",
    image: "/images/catalog/men/shoes/men-shoes.jpeg",
    description: "Premium footwear for the sophisticated gentleman",
    gradient: "from-stone-600 to-neutral-700",
    href: "/men/shoes"
  },
  {
    id: 6,
    name: "Children Shoes",
    image: "/images/catalog/men/children/children-shoes.jpeg",
    description: "Comfortable and stylish shoes for kids",
    gradient: "from-emerald-500 to-teal-600",
    href: "https://a202211011505300820001582.wsxcme.com/static/index.html#/goods_list/_doQoQdE1IxUpwM4XUK8yBVkhlBszn89w-fu68Kw?tagId=60348656"
  }
]

function CategoryCard({ category, isSmall }: { category: typeof categories[0], isSmall: boolean }) {
  const [imageError, setImageError] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const CardContent = () => (
    <div className={`group relative bg-white shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden ${
      isSmall ? 'rounded-xl' : 'rounded-2xl'
    }`}>
      {/* Image Container */}
      <div className={`relative overflow-hidden ${
        isSmall ? 'h-40 rounded-t-xl' : 'h-64 rounded-t-2xl'
      }`}>
        {/* Fallback gradient background */}
        <div className={`absolute inset-0 bg-gradient-to-br ${category.gradient} ${imageError ? 'opacity-90' : 'opacity-30'}`}></div>
        
        {/* Main Image */}
        {!imageError && (
          <Image
            src={category.image}
            alt={category.name}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-500"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            onError={() => setImageError(true)}
          />
        )}
        
        {/* Category Icon for fallback */}
        {imageError && (
          <div className="absolute inset-0 flex items-center justify-center z-20">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
              <span className="text-2xl text-white font-bold">
                {category.name.charAt(0)}
              </span>
            </div>
          </div>
        )}
        

        
        {/* Overlay on hover */}
        <div className="absolute inset-0 bg-gradient-to-t from-orange-600/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-30">
        </div>
      </div>

      {/* Content */}
      <div className={`${isSmall ? 'p-3' : 'p-6'}`}>
        <h3 className={`font-bold text-gray-900 group-hover:text-orange-600 transition-colors duration-200 ${
          isSmall ? 'text-sm mb-1 text-center' : 'text-xl mb-2'
        }`}>
          {category.name}
        </h3>
        {!isSmall && (
          <p className="text-gray-600 text-sm leading-relaxed">
            {category.description}
          </p>
        )}
      </div>

      {/* Decorative element */}
      <div className="absolute top-4 right-4 w-8 h-8 bg-orange-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </div>
    </div>
  )

  return category.href ? (
    <Link href={category.href} className="block">
      <CardContent />
    </Link>
  ) : (
    <CardContent />
  )
}

export default function MenPage() {
  const [isSmallView, setIsSmallView] = useState(false)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-8 sm:py-20 px-4">
        <div className="container mx-auto">
          {/* Filter Button - Mobile Top */}
          <div className="flex justify-end mb-4 sm:hidden">
            <button
              onClick={() => setIsSmallView(!isSmallView)}
              className="bg-orange-600 text-white p-2 rounded-lg hover:bg-orange-700 transition-colors duration-200 shadow-lg flex items-center gap-2 text-xs"
              title={isSmallView ? "Switch to Large View" : "Switch to Small View"}
            >
              {isSmallView ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>

          <div className="text-center">
            <div className="max-w-4xl mx-auto relative">
              {/* Filter Button - Desktop */}
              <div className="hidden sm:flex justify-end mb-4">
                <button
                  onClick={() => setIsSmallView(!isSmallView)}
                  className="bg-orange-600 text-white p-3 rounded-lg hover:bg-orange-700 transition-colors duration-200 shadow-lg flex items-center gap-2 text-sm"
                  title={isSmallView ? "Switch to Large View" : "Switch to Small View"}
                >
                  {isSmallView ? (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                    </svg>
                  )}
                </button>
              </div>

              <h1 className="text-3xl sm:text-5xl md:text-6xl font-bold text-gray-900 mb-4 sm:mb-6">
                Men's
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 ml-2 sm:ml-4">
                  Collection
                </span>
              </h1>
              <h2 className="text-2xl sm:text-4xl font-bold text-gray-900 mb-3 sm:mb-4">Shop by Category</h2>
              <p className="text-base sm:text-xl text-gray-600 mb-6 sm:mb-8 leading-relaxed px-4 sm:px-0">
                Explore our curated collection of men's essentials, from premium accessories
                to travel gear, all designed with quality and style in mind.
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-orange-400 to-orange-600 mx-auto rounded-full"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Grid */}
      <section className="pt-4 pb-16 px-4">
        <div className="container mx-auto">

          <div className={`grid max-w-6xl mx-auto ${
            isSmallView
              ? 'grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4'
              : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'
          }`}>
            {categories.map((category) => (
              <CategoryCard key={category.id} category={category} isSmall={isSmallView} />
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-gray-800 via-gray-900 to-black">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl font-bold text-white mb-6">
              Elevate Your Style Game
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Join the ranks of distinguished gentlemen who choose Best Lyfe Fashion 
              for their premium accessories and lifestyle essentials.
            </p>
            <div className="flex justify-center">
              <button className="bg-orange-600 text-white px-8 py-4 rounded-full font-bold hover:bg-orange-700 transition-colors duration-200 shadow-lg">
                Shop All Men's Items
              </button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

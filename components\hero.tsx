import Link from 'next/link'

export default function Hero() {
  const brands = [
    "CHANEL",
    "YSL",
    "FENDI",
    "LOUBOUTIN",
    "VALENTINO",
    "D&G",
    "BURBERRY",
    "PRADA",
    "BALENCIAGA",
    "VERSACE",
    "LOUIS VUITTON",
  ]

  return (
    <section className="relative bg-gradient-to-r from-orange-300 via-orange-400 to-orange-500 animate-gradient-x overflow-hidden">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-black mb-6">Best Lyfe Fashion</h2>
          <p className="text-xl text-black/80 mb-8">Discover luxury fashion from the world's most prestigious brands</p>

          <div className="mb-8">
            <p className="text-sm text-black/70 mb-4">Featured Luxury Brands:</p>
            <div className="flex flex-wrap gap-2 justify-center">
              {brands.map((brand, index) => (
                <div
                  key={index}
                  className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-black/10 text-black hover:bg-black/20 transition-colors"
                >
                  {brand}
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-center">
            <Link href="/collection" className="ring-offset-background focus-visible:outline-hidden focus-visible:ring-ring inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-11 rounded-md px-8 bg-black text-white hover:bg-gray-800">
              View Complete Store
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}
